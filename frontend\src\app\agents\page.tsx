"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface Agent {
  id: string
  name: string
  description: string
  status: 'active' | 'inactive' | 'draft'
  createdAt: string
  lastRun?: string
}

const SAMPLE_AGENTS: Agent[] = [
  {
    id: '1',
    name: 'Customer Support Bot',
    description: 'Handles customer inquiries and provides instant support',
    status: 'active',
    createdAt: '2024-01-15',
    lastRun: '2024-01-20'
  },
  {
    id: '2',
    name: 'Lead Generation Assistant',
    description: 'Qualifies leads and schedules meetings automatically',
    status: 'active',
    createdAt: '2024-01-10',
    lastRun: '2024-01-19'
  },
  {
    id: '3',
    name: 'Content Creator',
    description: 'Generates blog posts and social media content',
    status: 'draft',
    createdAt: '2024-01-18'
  }
]

export default function AgentsPage() {
  const [agents, setAgents] = useState<Agent[]>(SAMPLE_AGENTS)
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null)

  const getStatusColor = (status: Agent['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-red-100 text-red-800'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const toggleAgentStatus = (id: string) => {
    setAgents(agents.map(agent => 
      agent.id === id 
        ? { ...agent, status: agent.status === 'active' ? 'inactive' : 'active' }
        : agent
    ))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">AI Agents</h1>
            <p className="text-gray-600">Manage your intelligent automation agents</p>
          </div>
          <Button>Create New Agent</Button>
        </div>
      </div>

      {/* Stats */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardDescription>Total Agents</CardDescription>
              <CardTitle className="text-2xl">{agents.length}</CardTitle>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardDescription>Active</CardDescription>
              <CardTitle className="text-2xl text-green-600">
                {agents.filter(a => a.status === 'active').length}
              </CardTitle>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardDescription>Inactive</CardDescription>
              <CardTitle className="text-2xl text-red-600">
                {agents.filter(a => a.status === 'inactive').length}
              </CardTitle>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardDescription>Drafts</CardDescription>
              <CardTitle className="text-2xl text-yellow-600">
                {agents.filter(a => a.status === 'draft').length}
              </CardTitle>
            </CardHeader>
          </Card>
        </div>

        {/* Agents Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {agents.map((agent) => (
            <Card key={agent.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{agent.name}</CardTitle>
                    <CardDescription className="mt-2">{agent.description}</CardDescription>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>
                    {agent.status}
                  </span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  <div>Created: {new Date(agent.createdAt).toLocaleDateString()}</div>
                  {agent.lastRun && (
                    <div>Last run: {new Date(agent.lastRun).toLocaleDateString()}</div>
                  )}
                </div>
                <div className="flex gap-2 mt-4">
                  <Button variant="outline" size="sm" className="flex-1">
                    Edit
                  </Button>
                  <Button 
                    variant={agent.status === 'active' ? 'destructive' : 'default'}
                    size="sm"
                    onClick={() => toggleAgentStatus(agent.id)}
                  >
                    {agent.status === 'active' ? 'Stop' : 'Start'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {agents.length === 0 && (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <h3 className="text-lg font-medium text-gray-900 mb-2">No agents yet</h3>
              <p className="text-gray-600 mb-6">
                Create your first AI agent to start automating your workflows
              </p>
              <Button>Create Your First Agent</Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
