{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "noPropertyAccessFromIndexSignature": true, "strict": true, "esModuleInterop": true, "resolveJsonModule": true, "moduleResolution": "node", "lib": ["ES2022"], "types": ["node", "jest"], "paths": {"@/*": ["src/*"], "@/auth/*": ["src/auth/*"], "@/users/*": ["src/users/*"], "@/agents/*": ["src/agents/*"], "@/tools/*": ["src/tools/*"], "@/providers/*": ["src/providers/*"], "@/landing/*": ["src/landing/*"], "@/payments/*": ["src/payments/*"], "@/analytics/*": ["src/analytics/*"], "@/email/*": ["src/email/*"], "@/common/*": ["src/common/*"], "@/config/*": ["src/config/*"], "@/database/*": ["src/database/*"], "@/websocket/*": ["src/websocket/*"]}}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist", "coverage", "**/*.spec.ts", "**/*.test.ts"]}