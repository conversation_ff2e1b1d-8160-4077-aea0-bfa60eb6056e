import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';

export interface LogContext {
  requestId?: string;
  userId?: string;
  method?: string;
  url?: string;
  ip?: string;
  userAgent?: string;
  [key: string]: unknown;
}

@Injectable()
export class LoggerService implements NestLoggerService {
  private readonly winston: winston.Logger;

  constructor(private readonly configService: ConfigService) {
    const logLevel = this.configService?.get<string>('LOG_LEVEL') || process.env['LOG_LEVEL'] || 'info';
    const nodeEnv = this.configService?.get<string>('NODE_ENV') || process.env['NODE_ENV'] || 'development';

    this.winston = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
        winston.format.printf(({ timestamp, level, message, context, stack, ...meta }) => {
          const logEntry = {
            timestamp,
            level,
            message,
            context,
            ...meta,
          };

          if (stack) {
            logEntry['stack'] = stack as string;
          }

          return JSON.stringify(logEntry, null, nodeEnv === 'development' ? 2 : 0);
        }),
      ),
      defaultMeta: {
        service: 'synapseai-backend',
        environment: nodeEnv,
      },
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
          ),
        }),
      ],
    });

    // Add file transport for production
    if (nodeEnv === 'production') {
      this.winston.add(
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
      );

      this.winston.add(
        new winston.transports.File({
          filename: 'logs/combined.log',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
      );
    }
  }

  log(message: string, context?: LogContext): void {
    this.winston.info(message, { context });
  }

  error(message: string, trace?: string, context?: LogContext): void {
    this.winston.error(message, { context, stack: trace });
  }

  warn(message: string, context?: LogContext): void {
    this.winston.warn(message, { context });
  }

  debug(message: string, context?: LogContext): void {
    this.winston.debug(message, { context });
  }

  verbose(message: string, context?: LogContext): void {
    this.winston.verbose(message, { context });
  }

  // Business logic specific logging methods
  logUserAction(
    userId: string,
    action: string,
    details?: Record<string, unknown>,
    requestId?: string,
  ): void {
    this.winston.info(`User action: ${action}`, {
      context: {
        userId,
        action,
        details,
        requestId,
        type: 'user_action',
      },
    });
  }

  logSecurityEvent(
    event: string,
    details: Record<string, unknown>,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
  ): void {
    const logLevel = severity === 'critical' || severity === 'high' ? 'error' : 'warn';

    this.winston.log(logLevel, `Security event: ${event}`, {
      context: {
        event,
        details,
        severity,
        type: 'security_event',
      },
    });
  }

  logPerformanceMetric(
    metric: string,
    value: number,
    unit: string,
    context?: Record<string, unknown>,
  ): void {
    this.winston.info(`Performance metric: ${metric}`, {
      context: {
        metric,
        value,
        unit,
        ...context,
        type: 'performance_metric',
      },
    });
  }

  logBusinessEvent(
    event: string,
    details: Record<string, unknown>,
    userId?: string,
  ): void {
    this.winston.info(`Business event: ${event}`, {
      context: {
        event,
        details,
        userId,
        type: 'business_event',
      },
    });
  }

  logApiCall(
    provider: string,
    endpoint: string,
    duration: number,
    success: boolean,
    details?: Record<string, unknown>,
  ): void {
    const message = `API call to ${provider}/${endpoint}`;
    const logLevel = success ? 'info' : 'error';

    this.winston.log(logLevel, message, {
      context: {
        provider,
        endpoint,
        duration,
        success,
        details,
        type: 'api_call',
      },
    });
  }

  logDatabaseQuery(
    operation: string,
    table: string,
    duration: number,
    success: boolean,
    details?: Record<string, unknown>,
  ): void {
    const message = `Database ${operation} on ${table}`;
    const logLevel = success ? 'debug' : 'error';

    this.winston.log(logLevel, message, {
      context: {
        operation,
        table,
        duration,
        success,
        details,
        type: 'database_query',
      },
    });
  }
}
