import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import * as crypto from 'crypto';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';

@Injectable()
export class CryptoService {
  private readonly bcryptRounds: number;
  private readonly encryptionKey: string;
  private readonly algorithm = 'aes-256-gcm';

  constructor(private readonly configService: ConfigService) {
    this.bcryptRounds = this.configService?.get<number>('BCRYPT_ROUNDS') || parseInt(process.env['BCRYPT_ROUNDS'] || '12', 10);
    this.encryptionKey = this.configService?.get<string>('SESSION_SECRET') || process.env['SESSION_SECRET'] || '';

    if (!this.encryptionKey || this.encryptionKey.length < 32) {
      throw new Error('SESSION_SECRET environment variable must be at least 32 characters long');
    }
  }

  /**
   * Hash password using bcrypt
   */
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.bcryptRounds);
  }

  /**
   * Verify password against hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * Generate secure random token
   */
  generateSecureToken(length = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate secure random string with specific characters
   */
  generateSecureString(length = 16, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {
    let result = '';
    const charactersLength = charset.length;

    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charactersLength);
      result += charset.charAt(randomIndex);
    }

    return result;
  }

  /**
   * Generate numeric code for verification
   */
  generateNumericCode(length = 6): string {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return crypto.randomInt(min, max + 1).toString();
  }

  /**
   * Encrypt sensitive data
   */
  encrypt(text: string): { encrypted: string; iv: string; tag: string } {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.encryptionKey);
    cipher.setAAD(Buffer.from('synapseai', 'utf8'));

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const tag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
    };
  }

  /**
   * Decrypt sensitive data
   */
  decrypt(encryptedData: { encrypted: string; iv: string; tag: string }): string {
    const decipher = crypto.createDecipher(this.algorithm, this.encryptionKey);
    decipher.setAAD(Buffer.from('synapseai', 'utf8'));
    decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));

    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
   * Generate TOTP secret for 2FA
   */
  generateTotpSecret(userEmail: string): {
    secret: string;
    qrCodeUrl: string;
    manualEntryKey: string;
  } {
    const secret = speakeasy.generateSecret({
      name: userEmail,
      issuer: 'SynapseAI',
      length: 32,
    });

    return {
      secret: secret.base32,
      qrCodeUrl: secret.otpauth_url || '',
      manualEntryKey: secret.base32,
    };
  }

  /**
   * Generate QR code for TOTP setup
   */
  async generateQrCode(otpauthUrl: string): Promise<string> {
    return QRCode.toDataURL(otpauthUrl);
  }

  /**
   * Verify TOTP token
   */
  verifyTotpToken(token: string, secret: string, window = 1): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window,
    });
  }

  /**
   * Generate backup codes for 2FA
   */
  generateBackupCodes(count = 10): string[] {
    const codes: string[] = [];

    for (let i = 0; i < count; i++) {
      // Generate 8-character alphanumeric codes
      const code = this.generateSecureString(8, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789');
      // Format as XXXX-XXXX for better readability
      const formattedCode = `${code.substring(0, 4)}-${code.substring(4, 8)}`;
      codes.push(formattedCode);
    }

    return codes;
  }

  /**
   * Hash backup codes for storage
   */
  async hashBackupCodes(codes: string[]): Promise<string[]> {
    const hashedCodes: string[] = [];

    for (const code of codes) {
      const hash = await this.hashPassword(code.replace('-', ''));
      hashedCodes.push(hash);
    }

    return hashedCodes;
  }

  /**
   * Verify backup code
   */
  async verifyBackupCode(code: string, hashedCodes: string[]): Promise<boolean> {
    const cleanCode = code.replace('-', '');

    for (const hashedCode of hashedCodes) {
      if (await this.verifyPassword(cleanCode, hashedCode)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Generate magic link token
   */
  generateMagicLinkToken(): {
    token: string;
    hashedToken: string;
    expiresAt: Date;
  } {
    const token = this.generateSecureToken(64);
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    return {
      token,
      hashedToken,
      expiresAt,
    };
  }

  /**
   * Verify magic link token
   */
  verifyMagicLinkToken(token: string, hashedToken: string): boolean {
    const computedHash = crypto.createHash('sha256').update(token).digest('hex');
    return crypto.timingSafeEqual(
      Buffer.from(computedHash, 'hex'),
      Buffer.from(hashedToken, 'hex'),
    );
  }

  /**
   * Generate API key
   */
  generateApiKey(): string {
    const prefix = 'sk_';
    const randomPart = this.generateSecureToken(32);
    return `${prefix}${randomPart}`;
  }

  /**
   * Hash API key for storage
   */
  async hashApiKey(apiKey: string): Promise<string> {
    return crypto.createHash('sha256').update(apiKey).digest('hex');
  }

  /**
   * Verify API key
   */
  async verifyApiKey(apiKey: string, hashedApiKey: string): Promise<boolean> {
    const computedHash = crypto.createHash('sha256').update(apiKey).digest('hex');
    return crypto.timingSafeEqual(
      Buffer.from(computedHash, 'hex'),
      Buffer.from(hashedApiKey, 'hex'),
    );
  }

  /**
   * Generate secure session ID
   */
  generateSessionId(): string {
    return this.generateSecureToken(48);
  }

  /**
   * Generate CSRF token
   */
  generateCsrfToken(): string {
    return this.generateSecureToken(32);
  }

  /**
   * Constant-time string comparison
   */
  constantTimeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    return crypto.timingSafeEqual(Buffer.from(a), Buffer.from(b));
  }
}
