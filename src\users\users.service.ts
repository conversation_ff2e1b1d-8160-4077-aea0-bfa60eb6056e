import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/database/prisma.service';
import { LoggerService } from '@/common/services/logger.service';
import { ResourceNotFoundException } from '@/common/filters/global-exception.filter';
import { UpdateProfileDto } from './dto/users.dto';

@Injectable()
export class UsersService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Get user profile
   */
  async getProfile(userId: string): Promise<{ success: true; data: any }> {
    const user = await this.prismaService['user'].findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      throw new ResourceNotFoundException('User', userId);
    }

    return {
      success: true,
      data: user,
    };
  }

  /**
   * Update user profile
   */
  async updateProfile(
    userId: string,
    updateProfileDto: UpdateProfileDto,
  ): Promise<{ success: true; data: any }> {
    const { name, avatar } = updateProfileDto;

    const user = await this.prismaService['user'].update({
      where: { id: userId },
      data: {
        ...(name && { name }),
        ...(avatar && { avatar }),
        updatedAt: new Date(),
      },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        updatedAt: true,
      },
    });

    // Log profile update
    this.loggerService.logUserAction(userId, 'profile_updated', {
      changes: updateProfileDto,
    });

    return {
      success: true,
      data: user,
    };
  }

  /**
   * Find user by ID
   */
  async findById(userId: string): Promise<any> {
    return this.prismaService['user'].findUnique({
      where: { id: userId },
    });
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<any> {
      return this.prismaService['user'].findUnique({
      where: { email },
    });
  }
}
