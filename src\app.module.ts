import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule, ThrottlerGuard, ThrottlerModuleOptions } from '@nestjs/throttler';
import { APP_GUARD } from '@nestjs/core';
import { JwtModule, JwtModuleOptions } from '@nestjs/jwt';

// Configuration
import { DatabaseModule } from '@/database/database.module';
import { RedisModule } from '@/database/redis.module';
import { configValidationSchema } from '@/config/config.validation';

// Core modules
import { AuthModule } from '@/auth/auth.module';
import { UsersModule } from '@/users/users.module';
import { AgentsModule } from '@/agents/agents.module';
import { ToolsModule } from '@/tools/tools.module';
import { ProvidersModule } from '@/providers/providers.module';
import { LandingModule } from '@/landing/landing.module';
import { PaymentsModule } from '@/payments/payments.module';
import { AnalyticsModule } from '@/analytics/analytics.module';
import { EmailModule } from '@/email/email.module';
import { WebsocketModule } from '@/websocket/websocket.module';

// Common modules
import { CommonModule } from '@/common/common.module';
import { HealthModule } from '@/common/health/health.module';

@Module({
  imports: [
    // Configuration module with validation
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      validationSchema: configValidationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: false,
      },
      cache: true,
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService): ThrottlerModuleOptions => ({
        throttlers: [
          {
            ttl: configService.get<number>('RATE_LIMIT_TTL', 60) || 60,
            limit: configService.get<number>('RATE_LIMIT_LIMIT', 100) || 100,
          },
        ],
      }),
    }),

    // JWT global configuration
    JwtModule.registerAsync({
      global: true,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService): JwtModuleOptions => ({
        secret: configService.get<string>('JWT_SECRET') || '',
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '15m'),
          issuer: 'synapseai',
          audience: 'synapseai-users',
        },
        verifyOptions: {
          issuer: 'synapseai',
          audience: 'synapseai-users',
        },
      }),
    }),

    // Database modules
    DatabaseModule,
    RedisModule,

    // Core application modules
    CommonModule,
    HealthModule,
    AuthModule,
    UsersModule,
    AgentsModule,
    ToolsModule,
    ProvidersModule,
    LandingModule,
    PaymentsModule,
    AnalyticsModule,
    EmailModule,
    WebsocketModule,
  ],
  providers: [
    // Global rate limiting guard
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {
  constructor(private readonly configService: ConfigService) {
    try {
      // Log important configuration on startup
      const nodeEnv = this.configService?.get<string>('NODE_ENV', 'development') || 'development';
      const port = this.configService?.get<number>('PORT', 3001) || 3001;
      const dbUrl = this.configService?.get<string>('DATABASE_URL');
      const redisUrl = this.configService?.get<string>('REDIS_URL');
      const jwtSecret = this.configService?.get<string>('JWT_SECRET');

      console.log('🔧 Application Configuration:');
      console.log(`   Environment: ${nodeEnv}`);
      console.log(`   Port: ${port}`);
      console.log(`   Database: ${dbUrl ? '✅ Connected' : '❌ Not configured'}`);
      console.log(`   Redis: ${redisUrl ? '✅ Connected' : '❌ Not configured'}`);
      console.log(`   JWT Secret: ${jwtSecret ? '✅ Set' : '❌ Missing'}`);
    } catch (error: any) {
      console.error('❌ Error loading configuration:', error.message);
    }
  }
}
