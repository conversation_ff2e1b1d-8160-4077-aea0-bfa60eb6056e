import { Module } from '@nestjs/common';
import { AuthController } from '@/auth/auth.controller';
import { AuthService } from '@/auth/auth.service';
import { JwtStrategy } from '@/auth/strategies/jwt.strategy';
import { PassportModule } from '@nestjs/passport';

// OAuth strategies are optional - only include if configured
const providers = [AuthService, JwtStrategy];

// Only add OAuth strategies if environment variables are configured
if (process.env['GOOGLE_CLIENT_ID'] && process.env['GOOGLE_CLIENT_SECRET']) {
  const { GoogleStrategy } = require('@/auth/strategies/google.strategy');
  providers.push(GoogleStrategy);
}

if (process.env['GITHUB_CLIENT_ID'] && process.env['GITHUB_CLIENT_SECRET']) {
  const { GithubStrategy } = require('@/auth/strategies/github.strategy');
  providers.push(GithubStrategy);
}

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
  ],
  controllers: [AuthController],
  providers,
  exports: [AuthService],
})
export class AuthModule { }
