import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as multer from 'multer';
import * as path from 'path';
import * as fs from 'fs/promises';
import sharp, { Sharp } from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import { ValidationException } from '@/common/filters/global-exception.filter';

export interface UploadedFile {
  id: string;
  originalName: string;
  filename: string;
  path: string;
  mimetype: string;
  size: number;
  url: string;
}

export interface ImageProcessingOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
}

@Injectable()
export class FileUploadService {
  private readonly uploadDir: string;
  private readonly maxFileSize: number;
  private readonly allowedMimeTypes: string[];

  constructor(private readonly configService: ConfigService) {
    this.uploadDir = this.configService?.get<string>('UPLOAD_DEST') || process.env['UPLOAD_DEST'] || './uploads';
    this.maxFileSize = this.configService?.get<number>('MAX_FILE_SIZE') || parseInt(process.env['MAX_FILE_SIZE'] || '10485760', 10); // 10MB
    this.allowedMimeTypes = (this.configService?.get<string>('ALLOWED_FILE_TYPES') || process.env['ALLOWED_FILE_TYPES'] || 'image/jpeg,image/png,image/gif,image/webp')
      .split(',');

    this.ensureUploadDirectory();
  }

  /**
   * Get multer configuration for file uploads
   */
  getMulterConfig(): multer.Options {
    return {
      storage: multer.diskStorage({
        destination: (req, file, cb) => {
          cb(null, this.uploadDir);
        },
        filename: (req, file, cb) => {
          const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
          const extension = path.extname(file.originalname);
          cb(null, `${file.fieldname}-${uniqueSuffix}${extension}`);
        },
      }),
      limits: {
        fileSize: this.maxFileSize,
        files: 10, // Maximum 10 files per request
      },
      fileFilter: (req, file, cb) => {
        if (this.allowedMimeTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(null, false);
        }
      },
    };
  }

  /**
   * Process uploaded file and return file information
   */
  async processUploadedFile(file: Express.Multer.File): Promise<UploadedFile> {
    const fileId = uuidv4();
    const extension = path.extname(file.originalname);
    const newFilename = `${fileId}${extension}`;
    const newPath = path.join(this.uploadDir, newFilename);

    // Move file to new location with UUID filename
    await fs.rename(file.path, newPath);

    // Generate file URL (this would be your CDN URL in production)
    const baseUrl = this.configService.get<string>('BASE_URL', 'http://localhost:3001');
    const fileUrl = `${baseUrl}/uploads/${newFilename}`;

    return {
      id: fileId,
      originalName: file.originalname,
      filename: newFilename,
      path: newPath,
      mimetype: file.mimetype,
      size: file.size,
      url: fileUrl,
    };
  }

  /**
   * Process and optimize image
   */
  async processImage(
    file: Express.Multer.File,
    options: ImageProcessingOptions = {},
  ): Promise<UploadedFile> {
    if (!file.mimetype.startsWith('image/')) {
      throw new ValidationException('File must be an image');
    }

    const fileId = uuidv4();
    const format = options.format || 'webp';
    const newFilename = `${fileId}.${format}`;
    const newPath = path.join(this.uploadDir, newFilename);

    // Process image with Sharp
    let sharpInstance = sharp(file.path as string) as Sharp;

    // Resize if dimensions provided
    if (options.width || options.height) {
      sharpInstance = sharpInstance.resize(options.width, options.height, {
        fit: options.fit || 'cover',
        withoutEnlargement: true,
      });
    }

    // Convert format and set quality
    switch (format) {
      case 'jpeg':
        sharpInstance = sharpInstance.jpeg({ quality: options.quality || 85 });
        break;
      case 'png':
        sharpInstance = sharpInstance.png({ quality: options.quality || 85 });
        break;
      case 'webp':
        sharpInstance = sharpInstance.webp({ quality: options.quality || 85 });
        break;
    }

    // Save processed image
    await sharpInstance.toFile(newPath);

    // Remove original file
    await fs.unlink(file.path);

    // Get file stats
    const stats = await fs.stat(newPath);

    // Generate file URL
    const baseUrl = this.configService.get<string>('BASE_URL', 'http://localhost:3001');
    const fileUrl = `${baseUrl}/uploads/${newFilename}`;

    return {
      id: fileId,
      originalName: file.originalname,
      filename: newFilename,
      path: newPath,
      mimetype: `image/${format}`,
      size: stats.size,
      url: fileUrl,
    };
  }

  /**
   * Generate multiple image sizes (thumbnails, medium, large)
   */
  async generateImageSizes(file: Express.Multer.File): Promise<{
    thumbnail: UploadedFile;
    medium: UploadedFile;
    large: UploadedFile;
    original: UploadedFile;
  }> {
    if (!file.mimetype.startsWith('image/')) {
      throw new ValidationException('File must be an image');
    }

    const fileId = uuidv4();
    const baseUrl = this.configService.get<string>('BASE_URL', 'http://localhost:3001');

    // Generate thumbnail (150x150)
    const thumbnailFilename = `${fileId}_thumb.webp`;
    const thumbnailPath = path.join(this.uploadDir, thumbnailFilename);
    await sharp(file.path as string)
      .resize(150, 150, { fit: 'cover' })
      .webp({ quality: 80 })
      .toFile(thumbnailPath);

    // Generate medium size (800x600)
    const mediumFilename = `${fileId}_medium.webp`;
    const mediumPath = path.join(this.uploadDir, mediumFilename);
    await sharp(file.path as string)
      .resize(800, 600, { fit: 'inside', withoutEnlargement: true })
      .webp({ quality: 85 })
      .toFile(mediumPath);

    // Generate large size (1920x1080)
    const largeFilename = `${fileId}_large.webp`;
    const largePath = path.join(this.uploadDir, largeFilename);
    await sharp(file.path as string)
      .resize(1920, 1080, { fit: 'inside', withoutEnlargement: true })
      .webp({ quality: 90 })
      .toFile(largePath);

    // Keep original
    const originalFilename = `${fileId}_original${path.extname(file.originalname)}`;
    const originalPath = path.join(this.uploadDir, originalFilename);
    await fs.rename(file.path, originalPath);

    // Get file stats
    const [thumbnailStats, mediumStats, largeStats, originalStats] = await Promise.all([
      fs.stat(thumbnailPath),
      fs.stat(mediumPath),
      fs.stat(largePath),
      fs.stat(originalPath),
    ]);

    return {
      thumbnail: {
        id: `${fileId}_thumb`,
        originalName: file.originalname,
        filename: thumbnailFilename,
        path: thumbnailPath,
        mimetype: 'image/webp',
        size: thumbnailStats.size,
        url: `${baseUrl}/uploads/${thumbnailFilename}`,
      },
      medium: {
        id: `${fileId}_medium`,
        originalName: file.originalname,
        filename: mediumFilename,
        path: mediumPath,
        mimetype: 'image/webp',
        size: mediumStats.size,
        url: `${baseUrl}/uploads/${mediumFilename}`,
      },
      large: {
        id: `${fileId}_large`,
        originalName: file.originalname,
        filename: largeFilename,
        path: largePath,
        mimetype: 'image/webp',
        size: largeStats.size,
        url: `${baseUrl}/uploads/${largeFilename}`,
      },
      original: {
        id: `${fileId}_original`,
        originalName: file.originalname,
        filename: originalFilename,
        path: originalPath,
        mimetype: file.mimetype,
        size: originalStats.size,
        url: `${baseUrl}/uploads/${originalFilename}`,
      },
    };
  }

  /**
   * Delete file from filesystem
   */
  async deleteFile(filename: string): Promise<void> {
    const filePath = path.join(this.uploadDir, filename);

    try {
      await fs.access(filePath);
      await fs.unlink(filePath);
    } catch (error) {
      // File doesn't exist, ignore error
    }
  }

  /**
   * Validate file before upload
   */
  validateFile(file: Express.Multer.File): void {
    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      throw new ValidationException(`File type ${file.mimetype} is not allowed`);
    }

    if (file.size > this.maxFileSize) {
      throw new ValidationException(`File size ${file.size} exceeds maximum allowed size ${this.maxFileSize}`);
    }
  }

  /**
   * Ensure upload directory exists
   */
  private async ensureUploadDirectory(): Promise<void> {
    try {
      await fs.access(this.uploadDir);
    } catch {
      await fs.mkdir(this.uploadDir, { recursive: true });
    }
  }
}
