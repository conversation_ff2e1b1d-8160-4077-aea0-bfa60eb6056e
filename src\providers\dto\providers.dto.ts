import { Is<PERSON>tring, IsO<PERSON>al, IsNumber, IsBoolean, IsArray, ValidateNested, <PERSON>In, <PERSON>, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class MessageDto {
    @ApiProperty({
        description: 'Message role',
        example: 'user',
        enum: ['system', 'user', 'assistant', 'function'],
    })
    @IsString()
    @IsIn(['system', 'user', 'assistant', 'function'])
    role!: 'system' | 'user' | 'assistant' | 'function';

    @ApiProperty({
        description: 'Message content',
        example: 'Hello, how can you help me today?',
    })
    @IsString()
    content!: string;

    @ApiProperty({
        description: 'Message name (for function calls)',
        example: 'get_weather',
        required: false,
    })
    @IsString()
    @IsOptional()
    name?: string;
}

export class ChatCompletionDto {
    @ApiProperty({
        description: 'Array of messages',
        type: [MessageDto],
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => MessageDto)
    messages!: MessageDto[];

    @ApiProperty({
        description: 'Model to use for completion',
        example: 'gpt-3.5-turbo',
        required: false,
    })
    @IsString()
    @IsOptional()
    model?: string;

    @ApiProperty({
        description: 'Provider to use (optional, will auto-select if not specified)',
        example: 'openai',
        required: false,
        enum: ['openai', 'groq', 'openrouter'],
    })
    @IsString()
    @IsOptional()
    @IsIn(['openai', 'groq', 'openrouter'])
    provider?: 'openai' | 'groq' | 'openrouter';

    @ApiProperty({
        description: 'Temperature for randomness (0-2)',
        example: 0.7,
        required: false,
        minimum: 0,
        maximum: 2,
    })
    @IsNumber()
    @IsOptional()
    @Min(0)
    @Max(2)
    temperature?: number;

    @ApiProperty({
        description: 'Maximum tokens to generate',
        example: 1000,
        required: false,
        minimum: 1,
        maximum: 128000,
    })
    @IsNumber()
    @IsOptional()
    @Min(1)
    @Max(128000)
    max_tokens?: number;

    @ApiProperty({
        description: 'Enable streaming response',
        example: false,
        required: false,
    })
    @IsBoolean()
    @IsOptional()
    stream?: boolean;
}

export class TestProviderDto {
    @ApiProperty({
        description: 'Test message to send',
        example: 'Hello, this is a test message.',
        required: false,
    })
    @IsString()
    @IsOptional()
    message?: string;

    @ApiProperty({
        description: 'Model to test with',
        example: 'gpt-3.5-turbo',
        required: false,
    })
    @IsString()
    @IsOptional()
    model?: string;
}

export class ProviderDto {
    @ApiProperty({
        description: 'Provider name',
        example: 'openai',
    })
    name!: string;

    @ApiProperty({
        description: 'Whether the provider is available',
        example: true,
    })
    isAvailable!: boolean;

    @ApiProperty({
        description: 'Number of available models',
        example: 5,
    })
    modelCount!: number;

    @ApiProperty({
        description: 'Whether the provider supports streaming',
        example: true,
    })
    supportsStreaming!: boolean;

    @ApiProperty({
        description: 'Whether the provider supports function calling',
        example: true,
    })
    supportsFunctions!: boolean;

    @ApiProperty({
        description: 'Maximum tokens supported',
        example: 128000,
    })
    maxTokens!: number;
}

export class ModelDto {
    @ApiProperty({
        description: 'Provider name',
        example: 'openai',
    })
    provider!: string;

    @ApiProperty({
        description: 'Model identifier',
        example: 'gpt-4',
    })
    model!: string;

    @ApiProperty({
        description: 'Human-readable model name',
        example: 'GPT-4 (OpenAI)',
    })
    displayName!: string;
}