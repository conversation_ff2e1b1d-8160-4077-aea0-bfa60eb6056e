import { Injectable, OnModuleInit, OnModule<PERSON><PERSON>roy, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, RedisClientType } from 'redis';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private client: RedisClientType;

  constructor(private readonly configService: ConfigService) {
    const redisUrl = this.configService?.get<string>('REDIS_URL') || process.env['REDIS_URL'];

    this.client = createClient({
      url: redisUrl || 'redis://localhost:6379',
      socket: {
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            this.logger.error('Redis connection failed after 10 retries');
            return new Error('Redis connection failed');
          }
          return Math.min(retries * 50, 1000);
        },
      },
    });

    // Error handling
    this.client.on('error', (error) => {
      this.logger.error('Redis client error:', error);
    });

    this.client.on('connect', () => {
      this.logger.log('✅ Redis client connected');
    });

    this.client.on('ready', () => {
      this.logger.log('✅ Redis client ready');
    });

    this.client.on('end', () => {
      this.logger.log('Redis client connection ended');
    });

    this.client.on('reconnecting', () => {
      this.logger.log('Redis client reconnecting...');
    });
  }

  async onModuleInit(): Promise<void> {
    try {
      await this.client.connect();
      this.logger.log('✅ Redis connected successfully');
    } catch (error) {
      this.logger.error('❌ Failed to connect to Redis', error);
      throw error;
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      await this.client.quit();
      this.logger.log('✅ Redis disconnected successfully');
    } catch (error) {
      this.logger.error('❌ Error disconnecting from Redis', error);
    }
  }

  /**
   * Ping Redis server
   */
  async ping(): Promise<string> {
    return this.client.ping();
  }

  /**
   * Set a key-value pair with optional expiration
   */
  async set(key: string, value: string, ttlSeconds?: number): Promise<string | null> {
    if (ttlSeconds) {
      return this.client.setEx(key, ttlSeconds, value);
    }
    return this.client.set(key, value);
  }

  /**
   * Get value by key
   */
  async get(key: string): Promise<string | null> {
    return this.client.get(key);
  }

  /**
   * Delete key(s)
   */
  async del(...keys: string[]): Promise<number> {
    return this.client.del(keys);
  }

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<number> {
    return this.client.exists(key);
  }

  /**
   * Set expiration for a key
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    return this.client.expire(key, seconds);
  }

  /**
   * Get time to live for a key
   */
  async ttl(key: string): Promise<number> {
    return this.client.ttl(key);
  }

  /**
   * Increment a numeric value
   */
  async incr(key: string): Promise<number> {
    return this.client.incr(key);
  }

  /**
   * Increment by a specific amount
   */
  async incrBy(key: string, increment: number): Promise<number> {
    return this.client.incrBy(key, increment);
  }

  /**
   * Decrement a numeric value
   */
  async decr(key: string): Promise<number> {
    return this.client.decr(key);
  }

  /**
   * Set JSON object
   */
  async setJson(key: string, value: object, ttlSeconds?: number): Promise<string | null> {
    const jsonString = JSON.stringify(value);
    return this.set(key, jsonString, ttlSeconds);
  }

  /**
   * Get JSON object
   */
  async getJson<T = unknown>(key: string): Promise<T | null> {
    const value = await this.get(key);
    if (!value) return null;

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      this.logger.error(`Failed to parse JSON for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Add to set
   */
  async sadd(key: string, ...members: string[]): Promise<number> {
    return this.client.sAdd(key, members);
  }

  /**
   * Remove from set
   */
  async srem(key: string, ...members: string[]): Promise<number> {
    return this.client.sRem(key, members);
  }

  /**
   * Check if member exists in set
   */
  async sismember(key: string, member: string): Promise<boolean> {
    return this.client.sIsMember(key, member);
  }

  /**
   * Get all members of a set
   */
  async smembers(key: string): Promise<string[]> {
    return this.client.sMembers(key);
  }

  /**
   * Push to list (left)
   */
  async lpush(key: string, ...elements: string[]): Promise<number> {
    return this.client.lPush(key, elements);
  }

  /**
   * Push to list (right)
   */
  async rpush(key: string, ...elements: string[]): Promise<number> {
    return this.client.rPush(key, elements);
  }

  /**
   * Pop from list (left)
   */
  async lpop(key: string): Promise<string | null> {
    return this.client.lPop(key);
  }

  /**
   * Pop from list (right)
   */
  async rpop(key: string): Promise<string | null> {
    return this.client.rPop(key);
  }

  /**
   * Get list length
   */
  async llen(key: string): Promise<number> {
    return this.client.lLen(key);
  }

  /**
   * Get list range
   */
  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    return this.client.lRange(key, start, stop);
  }

  /**
   * Set hash field
   */
  async hset(key: string, field: string, value: string): Promise<number> {
    return this.client.hSet(key, field, value);
  }

  /**
   * Get hash field
   */
  async hget(key: string, field: string): Promise<string | undefined> {
    return this.client.hGet(key, field);
  }

  /**
   * Get all hash fields and values
   */
  async hgetall(key: string): Promise<Record<string, string>> {
    return this.client.hGetAll(key);
  }

  /**
   * Delete hash field
   */
  async hdel(key: string, ...fields: string[]): Promise<number> {
    return this.client.hDel(key, fields);
  }

  /**
   * Check if hash field exists
   */
  async hexists(key: string, field: string): Promise<boolean> {
    return this.client.hExists(key, field);
  }

  /**
   * Get keys matching pattern
   */
  async keys(pattern: string): Promise<string[]> {
    return this.client.keys(pattern);
  }

  /**
   * Flush all data (use with caution!)
   */
  async flushall(): Promise<string> {
    return this.client.flushAll();
  }

  /**
   * Get Redis info
   */
  async info(section?: string): Promise<string> {
    return this.client.info(section);
  }
}
