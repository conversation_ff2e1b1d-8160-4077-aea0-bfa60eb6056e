import { Injectable, Logger } from '@nestjs/common';
import { AIProviderFactory, ProviderName } from '@/providers/ai/ai-provider.factory';
import { ChatCompletionDto, TestProviderDto, ModelDto } from '@/providers/dto/providers.dto';
import { AICompletionRequest, AIProviderUsage } from '@/providers/interfaces/ai-provider.interface';

@Injectable()
export class ProvidersService {
  private readonly logger = new Logger(ProvidersService.name);

  constructor(private readonly aiProviderFactory: AIProviderFactory) {}

  /**
   * Get all available providers with their status
   */
  async getProviders(): Promise<{ success: true; data: any }> {
    const stats = this.aiProviderFactory.getProviderStats();
    const availableCount = stats.filter(p => p.isAvailable).length;

    return {
      success: true,
      data: {
        providers: stats,
        totalProviders: stats.length,
        availableProviders: availableCount,
      },
    };
  }

  /**
   * Get all available models from all providers
   */
  async getModels(): Promise<{ success: true; data: ModelDto[] }> {
    const models = this.aiProviderFactory.getAllAvailableModels();

    return {
      success: true,
      data: models,
    };
  }

  /**
   * Get models for a specific provider
   */
  async getProviderModels(providerName: string): Promise<{ success: true; data: ModelDto[] }> {
    try {
      const provider = this.aiProviderFactory.getProvider(providerName as ProviderName);
      const models = provider.getModels().map(model => ({
        provider: providerName,
        model,
        displayName: `${model} (${providerName})`,
      }));

      return {
        success: true,
        data: models,
      };
    } catch (error) {
      throw new Error(`Provider '${providerName}' not found or not available`);
    }
  }

  /**
   * Test all provider connections
   */
  async testConnections(): Promise<{ success: true; data: Record<ProviderName, boolean> }> {
    const results = await this.aiProviderFactory.testAllConnections();

    return {
      success: true,
      data: results,
    };
  }

  /**
   * Test specific provider connection
   */
  async testProvider(
    providerName: string,
    testDto: TestProviderDto,
  ): Promise<{ success: true; data: any }> {
    try {
      const provider = this.aiProviderFactory.getProvider(providerName as ProviderName);
      const testMessage = testDto.message || 'Hello, this is a test message.';
      const model = testDto.model || provider.getModels()[0];

      const request: AICompletionRequest = {
        messages: [
          {
            role: 'user',
            content: testMessage,
          },
        ],
        model,
        max_tokens: 50,
        temperature: 0.7,
      };

      const startTime = Date.now();
      const response = await provider.createCompletion(request);
      const responseTime = Date.now() - startTime;

      return {
        success: true,
        data: {
          provider: providerName,
          model,
          responseTime,
          response: response.choices[0]?.message?.content || 'No response',
          usage: response.usage,
        },
      };
    } catch (error) {
      this.logger.error(`Provider test failed for ${providerName}:`, error);
      throw new Error(`Provider test failed: ${error['message']}`);
    }
  }

  /**
   * Create chat completion using any available provider
   */
  async createChatCompletion(chatDto: ChatCompletionDto): Promise<{ success: true; data: any }> {
    try {
      let provider;
      
      if (chatDto.provider) {
        // Use specified provider
        provider = this.aiProviderFactory.getProvider(chatDto.provider);
      } else if (chatDto.model) {
        // Auto-select provider based on model
        provider = this.aiProviderFactory.getProviderForModel(chatDto.model);
      } else {
        // Use best available provider
        provider = this.aiProviderFactory.getBestAvailableProvider();
      }

      const request: AICompletionRequest = {
        messages: chatDto.messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          ...(msg.name && { name: msg.name }),
        })),
        model: chatDto.model || provider.getModels()[0],
        temperature: chatDto.temperature,
        max_tokens: chatDto.max_tokens,
        stream: chatDto.stream || false,
      };

      const startTime = Date.now();
      const response = await provider.createCompletion(request);
      const responseTime = Date.now() - startTime;

      // Calculate cost
      const cost = provider.calculateCost(response.usage as AIProviderUsage, request.model!);

      return {
        success: true,
        data: {
          ...response,
          provider: provider.name,
          responseTime,
          usage: {
            ...response.usage,
            cost,
          },
        },
      };
    } catch (error) {
      this.logger.error('Chat completion failed:', error);
      throw new Error(`Chat completion failed: ${error['message']}`);
    }
  }

  /**
   * Get usage statistics (placeholder implementation)
   */
  async getUsageStats(days?: number): Promise<{ success: true; data: any }> {
    // This would typically come from a database
    const period = days || 30;
    
    return {
      success: true,
      data: {
        period: `${period} days`,
        providers: [
          {
            provider: 'openai',
            totalRequests: 150,
            totalTokens: 45000,
            totalCost: 0.67,
            averageResponseTime: 1250,
          },
          {
            provider: 'groq',
            totalRequests: 75,
            totalTokens: 22000,
            totalCost: 0.02,
            averageResponseTime: 800,
          },
        ],
        summary: {
          totalRequests: 225,
          totalTokens: 67000,
          totalCost: 0.69,
          averageResponseTime: 1100,
        },
      },
    };
  }
}
