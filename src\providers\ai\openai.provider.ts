import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import {
  BaseAIProvider,
  AICompletionRequest,
  AICompletionResponse,
  AIStreamChunk,
  AIProviderConfig,
  AIProviderUsage,
} from '../interfaces/ai-provider.interface';

@Injectable()
export class OpenAIProvider extends BaseAIProvider {
  readonly name = 'openai';
  private readonly logger = new Logger(OpenAIProvider.name);
  private client: OpenAI | null = null;
  readonly config: AIProviderConfig;

  constructor(private readonly configService: ConfigService) {
    super();

    const apiKey = this.configService?.get<string>('OPENAI_API_KEY') || process.env['OPENAI_API_KEY'];
    const organization = this.configService?.get<string>('OPENAI_ORGANIZATION') || process.env['OPENAI_ORGANIZATION'];

    this.config = {
      name: this.name,
      apiKey,
      models: [
        'gpt-4-turbo-preview',
        'gpt-4',
        'gpt-4-32k',
        'gpt-3.5-turbo',
        'gpt-3.5-turbo-16k',
      ],
      maxTokens: 128000,
      supportsStreaming: true,
      supportsFunctions: true,
      isAvailable: !!apiKey,
    };

    if (this.isAvailable()) {
      this.client = new OpenAI({
        apiKey,
        organization,
      });
      this.logger.log('✅ OpenAI provider initialized successfully');
    } else {
      this.logger.warn('⚠️ OpenAI provider not available - missing API key');
    }
  }

  isAvailable(): boolean {
    return this.config.isAvailable;
  }

  getModels(): string[] {
    return this.config.models;
  }

  validateConfig(): boolean {
    return !!this.config.apiKey;
  }

  async createCompletion(request: AICompletionRequest): Promise<AICompletionResponse> {
    if (!this.client) {
      throw new Error('OpenAI client not initialized - missing API key');
    }

    try {
      const response = await this.client.chat.completions.create({
        model: request.model || 'gpt-3.5-turbo',
        messages: this.formatMessages(request.messages),
        temperature: request.temperature,
        max_tokens: request.max_tokens,
        top_p: request.top_p,
        frequency_penalty: request.frequency_penalty,
        presence_penalty: request.presence_penalty,
        stop: request.stop,
        stream: false,
        ...(request.functions && { functions: request.functions }),
        ...(request.function_call && { function_call: request.function_call }),
      });

      return {
        id: response.id,
        object: response.object,
        created: response.created,
        model: response.model,
        choices: response.choices.map(choice => ({
          index: choice.index,
          message: {
            role: choice.message.role as any,
            content: choice.message.content || '',
            ...(choice.message.function_call && {
              function_call: choice.message.function_call,
            }),
          },
          finish_reason: choice.finish_reason || '',
        })),
        usage: {
          prompt_tokens: response.usage?.prompt_tokens || 0,
          completion_tokens: response.usage?.completion_tokens || 0,
          total_tokens: response.usage?.total_tokens || 0,
        },
      };
    } catch (error) {
      this.handleError(error, 'completion failed');
    }
  }

  async *createStreamCompletion(request: AICompletionRequest): AsyncIterable<AIStreamChunk> {
    if (!this.client) {
      throw new Error('OpenAI client not initialized - missing API key');
    }

    try {
      const stream = await this.client.chat.completions.create({
        model: request.model || 'gpt-3.5-turbo',
        messages: this.formatMessages(request.messages),
        temperature: request.temperature,
        max_tokens: request.max_tokens,
        top_p: request.top_p,
        frequency_penalty: request.frequency_penalty,
        presence_penalty: request.presence_penalty,
        stop: request.stop,
        stream: true,
        ...(request.functions && { functions: request.functions }),
        ...(request.function_call && { function_call: request.function_call }),
      });

      for await (const chunk of stream) {
        yield {
          id: chunk.id,
          object: chunk.object,
          created: chunk.created,
          model: chunk.model,
          choices: chunk.choices.map(choice => ({
            index: choice.index,
            delta: {
              role: choice.delta.role,
              content: choice.delta.content,
              ...(choice.delta.function_call && {
                function_call: choice.delta.function_call,
              }),
            },
            finish_reason: choice.finish_reason,
          })),
        };
      }
    } catch (error) {
      this.handleError(error, 'stream completion failed');
    }
  }

  calculateCost(usage: AIProviderUsage, model: string): number {
    // OpenAI pricing (as of 2024)
    const pricing: Record<string, { input: number; output: number }> = {
      'gpt-4-turbo-preview': { input: 0.01, output: 0.03 },
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-4-32k': { input: 0.06, output: 0.12 },
      'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
      'gpt-3.5-turbo-16k': { input: 0.003, output: 0.004 },
    };

    const modelPricing = pricing[model] || pricing['gpt-3.5-turbo'];
    const inputCost = (usage.promptTokens / 1000) * modelPricing.input;
    const outputCost = (usage.completionTokens / 1000) * modelPricing.output;

    return inputCost + outputCost;
  }

  async testConnection(): Promise<boolean> {
    if (!this.client) {
      return false;
    }

    try {
      await this.client.models.list();
      return true;
    } catch (error) {
      this.logger.error('OpenAI connection test failed:', error);
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    if (!this.client) {
      return this.config.models;
    }

    try {
      const response = await this.client.models.list();
      return response.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      this.logger.warn('Failed to fetch OpenAI models, using defaults:', error);
      return this.config.models;
    }
  }
}
