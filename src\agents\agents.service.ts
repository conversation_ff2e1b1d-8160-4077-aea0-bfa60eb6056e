import { Injectable, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '@/database/prisma.service';
import { LoggerService } from '@/common/services/logger.service';
import { ValidationService } from '@/common/services/validation.service';
import { ResourceNotFoundException } from '@/common/filters/global-exception.filter';
import { CreateAgentDto, UpdateAgentDto } from './dto/agents.dto';
import { extractPaginationParams, createPaginatedResponse } from '@/common/interceptors/response.interceptor';

@Injectable()
export class AgentsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly loggerService: LoggerService,
    private readonly validationService: ValidationService,
  ) {}

  /**
   * Create a new agent
   */
  async createAgent(
    userId: string,
    createAgentDto: CreateAgentDto,
  ): Promise<{ success: true; data: any }> {
    const { name, description, flowData } = createAgentDto;

    // Validate flow data if provided
    if (flowData) {
      const flowValidation = this.validationService.validateAgentFlow(flowData);
      if (!flowValidation.isValid) {
        throw new Error(`Invalid flow data: ${flowValidation.errors.join(', ')}`);
      }
    }

    const agent = await this.prismaService['agent'].create({
      data: {
        name,
        description,
        flowData: flowData || { nodes: [], connections: [] },
        userId,
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
        flowData: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Log agent creation
    this.loggerService.logUserAction(userId, 'agent_created', {
      agentId: agent.id,
      agentName: agent.name,
    });

    return {
      success: true,
      data: agent,
    };
  }

  /**
   * Get user agents with pagination
   */
  async getAgents(
    userId: string,
    page?: number,
    limit?: number,
  ): Promise<{ success: true; data: any; meta: any }> {
    const { page: validPage, limit: validLimit, skip } = extractPaginationParams({
      page,
      limit,
    });

    const [agents, total] = await Promise.all([
      this.prismaService['agent'].findMany({
        where: {
          userId,
          deletedAt: null,
        },
        select: {
          id: true,
          name: true,
          description: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: validLimit,
      }),
      this.prismaService['agent'].count({
        where: {
          userId,
          deletedAt: null,
        },
      }),
    ]);

    const paginatedResponse = createPaginatedResponse(agents, validPage, validLimit, total);

    return {
      success: true,
      ...paginatedResponse,
    };
  }

  /**
   * Get agent by ID
   */
  async getAgent(userId: string, agentId: string): Promise<{ success: true; data: any }> {
    const agent = await this.prismaService['agent'].findFirst({
      where: {
        id: agentId,
        userId,
        deletedAt: null,
      },
      select: {
        id: true,
        name: true,
        description: true,
        flowData: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!agent) {
      throw new ResourceNotFoundException('Agent', agentId);
    }

    return {
      success: true,
      data: agent,
    };
  }

  /**
   * Update agent
   */
  async updateAgent(
    userId: string,
    agentId: string,
    updateAgentDto: UpdateAgentDto,
  ): Promise<{ success: true; data: any }> {
    const { name, description, flowData, isActive } = updateAgentDto;

    // Check if agent exists and belongs to user
    const existingAgent = await this.prismaService['agent'].findFirst({
      where: {
        id: agentId,
        userId,
        deletedAt: null,
      },
    });

    if (!existingAgent) {
      throw new ResourceNotFoundException('Agent', agentId);
    }

    // Validate flow data if provided
    if (flowData) {
      const flowValidation = this.validationService.validateAgentFlow(flowData);
      if (!flowValidation.isValid) {
        throw new Error(`Invalid flow data: ${flowValidation.errors.join(', ')}`);
      }
    }

    const agent = await this.prismaService['agent'].update({
      where: { id: agentId },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(flowData && { flowData }),
        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        description: true,
        flowData: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Log agent update
    this.loggerService.logUserAction(userId, 'agent_updated', {
      agentId: agent.id,
      agentName: agent.name,
      changes: updateAgentDto,
    });

    return {
      success: true,
      data: agent,
    };
  }

  /**
   * Delete agent (soft delete)
   */
  async deleteAgent(userId: string, agentId: string): Promise<{ success: true; data: any }> {
    // Check if agent exists and belongs to user
    const existingAgent = await this.prismaService['agent'].findFirst({
      where: {
        id: agentId,
        userId,
        deletedAt: null,
      },
    });

    if (!existingAgent) {
      throw new ResourceNotFoundException('Agent', agentId);
    }

    await this.prismaService['agent'].update({
      where: { id: agentId },
      data: {
        deletedAt: new Date(),
      },
    });

    // Log agent deletion
    this.loggerService.logUserAction(userId, 'agent_deleted', {
      agentId,
      agentName: existingAgent.name,
    });

    return {
      success: true,
      data: {
        message: 'Agent deleted successfully',
      },
    };
  }

  /**
   * Find agent by ID (internal use)
   */
  async findById(agentId: string): Promise<any> {
    return this.prismaService['agent'].findUnique({
      where: { id: agentId },
    });
  }
}
