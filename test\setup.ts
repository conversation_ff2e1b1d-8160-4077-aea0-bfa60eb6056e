import { ConfigService } from '@nestjs/config';

// Set test environment
process.env['NODE_ENV'] = 'test';

// Mock ConfigService for tests
jest.mock('@nestjs/config');

const mockConfigService = {
  get: jest.fn((key: string, defaultValue?: unknown) => {
    const config: Record<string, unknown> = {
      NODE_ENV: 'test',
      PORT: 3001,
      API_PREFIX: 'api/v1',
      CORS_ORIGIN: 'http://localhost:3000',
      DATABASE_URL: 'postgresql://test:test@localhost:5432/synapseai_test',
      REDIS_URL: 'redis://localhost:6379/1',
      JWT_SECRET: 'test-jwt-secret-key-for-testing-purposes-only',
      JWT_REFRESH_SECRET: 'test-refresh-secret-key-for-testing-purposes-only',
      JWT_EXPIRES_IN: '15m',
      JWT_REFRESH_EXPIRES_IN: '7d',
      BCRYPT_ROUNDS: 4, // Lower for faster tests
      SESSION_SECRET: 'test-session-secret-key-for-testing-purposes-only',
      COOKIE_SECRET: 'test-cookie-secret-key-for-testing-purposes-only',
      MAX_FILE_SIZE: 10485760,
      UPLOAD_DEST: './test-uploads',
      ALLOWED_FILE_TYPES: 'image/jpeg,image/png,image/gif,image/webp',
      RATE_LIMIT_TTL: 60,
      RATE_LIMIT_LIMIT: 100,
      LOG_LEVEL: 'error',
      ENABLE_REGISTRATION: true,
      ENABLE_EMAIL_VERIFICATION: false, // Disable for tests
      ENABLE_2FA: false, // Disable for tests
      ENABLE_MAGIC_LINKS: false, // Disable for tests
      ENABLE_OAUTH: false, // Disable for tests
      SWAGGER_ENABLED: false,
      DEBUG_MODE: false,
    };

    return config[key] ?? defaultValue;
  }),
};

(ConfigService as jest.MockedClass<typeof ConfigService>).mockImplementation(
  () => mockConfigService as any,
);

// Global test timeout
jest.setTimeout(30000);

// Mock external services
jest.mock('nodemailer', () => ({
  createTransporter: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  })),
}));

jest.mock('twilio', () => ({
  Twilio: jest.fn(() => ({
    messages: {
      create: jest.fn().mockResolvedValue({ sid: 'test-message-sid' }),
    },
  })),
}));

jest.mock('stripe', () => ({
  Stripe: jest.fn(() => ({
    customers: {
      create: jest.fn().mockResolvedValue({ id: 'cus_test' }),
      retrieve: jest.fn().mockResolvedValue({ id: 'cus_test' }),
    },
    subscriptions: {
      create: jest.fn().mockResolvedValue({ id: 'sub_test' }),
      retrieve: jest.fn().mockResolvedValue({ id: 'sub_test' }),
    },
    paymentMethods: {
      attach: jest.fn().mockResolvedValue({ id: 'pm_test' }),
    },
  })),
}));

jest.mock('openai', () => ({
  OpenAI: jest.fn(() => ({
    chat: {
      completions: {
        create: jest.fn().mockResolvedValue({
          choices: [
            {
              message: {
                content: 'Test AI response',
              },
            },
          ],
        }),
      },
    },
  })),
}));

// Mock Redis
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    quit: jest.fn().mockResolvedValue(undefined),
    ping: jest.fn().mockResolvedValue('PONG'),
    set: jest.fn().mockResolvedValue('OK'),
    get: jest.fn().mockResolvedValue(null),
    del: jest.fn().mockResolvedValue(1),
    exists: jest.fn().mockResolvedValue(0),
    expire: jest.fn().mockResolvedValue(true),
    ttl: jest.fn().mockResolvedValue(-1),
    incr: jest.fn().mockResolvedValue(1),
    on: jest.fn(),
  })),
}));

// Mock Prisma
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn(() => ({
    $connect: jest.fn().mockResolvedValue(undefined),
    $disconnect: jest.fn().mockResolvedValue(undefined),
    $queryRaw: jest.fn().mockResolvedValue([]),
    $transaction: jest.fn().mockImplementation((fn) => fn({})),
    user: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    agent: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
  })),
}));

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Global test utilities
(global as any).testUtils = {
  createMockUser: () => ({
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    createdAt: new Date(),
    updatedAt: new Date(),
  }),
  
  createMockAgent: () => ({
    id: 'test-agent-id',
    name: 'Test Agent',
    description: 'Test agent description',
    userId: 'test-user-id',
    flowData: {
      nodes: [],
      connections: [],
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  }),
  
  createMockRequest: (overrides = {}) => ({
    headers: {
      'x-request-id': 'test-request-id',
      'user-agent': 'test-agent',
    },
    ip: '127.0.0.1',
    method: 'GET',
    url: '/test',
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
    },
    ...overrides,
  }),
  
  createMockResponse: () => ({
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    setHeader: jest.fn().mockReturnThis(),
    statusCode: 200,
  }),
};

// Extend global types
declare global {
  namespace NodeJS {
    interface Global {
      testUtils: {
        createMockUser: () => any;
        createMockAgent: () => any;
        createMockRequest: (overrides?: any) => any;
        createMockResponse: () => any;
      };
    }
  }
}
