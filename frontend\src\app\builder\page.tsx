"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface PageElement {
  id: string
  type: 'hero' | 'features' | 'testimonials' | 'cta' | 'text' | 'image'
  content: any
  position: number
}

const ELEMENT_TEMPLATES = {
  hero: {
    title: "Your Amazing Headline",
    subtitle: "A compelling subtitle that explains your value proposition",
    buttonText: "Get Started",
    backgroundImage: ""
  },
  features: {
    title: "Key Features",
    items: [
      { title: "Feature 1", description: "Description of feature 1" },
      { title: "Feature 2", description: "Description of feature 2" },
      { title: "Feature 3", description: "Description of feature 3" }
    ]
  },
  testimonials: {
    title: "What Our Customers Say",
    items: [
      { name: "<PERSON>e", company: "Company Inc", text: "This product is amazing!" }
    ]
  },
  cta: {
    title: "Ready to Get Started?",
    subtitle: "Join thousands of satisfied customers",
    buttonText: "Start Free Trial"
  },
  text: {
    content: "Add your text content here..."
  },
  image: {
    src: "",
    alt: "Image description",
    caption: ""
  }
}

export default function PageBuilder() {
  const [elements, setElements] = useState<PageElement[]>([])
  const [selectedElement, setSelectedElement] = useState<string | null>(null)

  const addElement = (type: keyof typeof ELEMENT_TEMPLATES) => {
    const newElement: PageElement = {
      id: `${type}-${Date.now()}`,
      type,
      content: ELEMENT_TEMPLATES[type],
      position: elements.length
    }
    setElements([...elements, newElement])
  }

  const removeElement = (id: string) => {
    setElements(elements.filter(el => el.id !== id))
  }

  const moveElement = (id: string, direction: 'up' | 'down') => {
    const elementIndex = elements.findIndex(el => el.id === id)
    if (elementIndex === -1) return

    const newElements = [...elements]
    const targetIndex = direction === 'up' ? elementIndex - 1 : elementIndex + 1

    if (targetIndex >= 0 && targetIndex < elements.length) {
      [newElements[elementIndex], newElements[targetIndex]] = [newElements[targetIndex], newElements[elementIndex]]
      setElements(newElements)
    }
  }

  const renderElement = (element: PageElement) => {
    switch (element.type) {
      case 'hero':
        return (
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20 px-8 text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-4">{element.content.title}</h1>
            <p className="text-xl mb-8 max-w-2xl mx-auto">{element.content.subtitle}</p>
            <Button size="lg" variant="secondary">{element.content.buttonText}</Button>
          </div>
        )
      case 'features':
        return (
          <div className="py-16 px-8">
            <h2 className="text-3xl font-bold text-center mb-12">{element.content.title}</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {element.content.items.map((item: any, index: number) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle>{item.title}</CardTitle>
                    <CardDescription>{item.description}</CardDescription>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>
        )
      case 'cta':
        return (
          <div className="bg-slate-100 py-16 px-8 text-center">
            <h2 className="text-3xl font-bold mb-4">{element.content.title}</h2>
            <p className="text-xl mb-8">{element.content.subtitle}</p>
            <Button size="lg">{element.content.buttonText}</Button>
          </div>
        )
      default:
        return (
          <div className="p-8 border-2 border-dashed border-gray-300 text-center">
            <p>Element type: {element.type}</p>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Page Builder</h1>
          <div className="flex gap-2">
            <Button variant="outline">Preview</Button>
            <Button>Publish</Button>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r p-6">
          <h2 className="text-lg font-semibold mb-4">Add Elements</h2>
          <div className="space-y-2">
            {Object.keys(ELEMENT_TEMPLATES).map((type) => (
              <Button
                key={type}
                variant="outline"
                className="w-full justify-start"
                onClick={() => addElement(type as keyof typeof ELEMENT_TEMPLATES)}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Button>
            ))}
          </div>

          {selectedElement && (
            <div className="mt-8">
              <h3 className="text-lg font-semibold mb-4">Element Settings</h3>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => moveElement(selectedElement, 'up')}
                >
                  Move Up
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => moveElement(selectedElement, 'down')}
                >
                  Move Down
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => {
                    removeElement(selectedElement)
                    setSelectedElement(null)
                  }}
                >
                  Delete
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Canvas */}
        <div className="flex-1 p-6">
          <div className="bg-white rounded-lg shadow-sm min-h-[600px]">
            {elements.length === 0 ? (
              <div className="flex items-center justify-center h-96 text-gray-500">
                <div className="text-center">
                  <p className="text-xl mb-2">Start building your page</p>
                  <p>Add elements from the sidebar to get started</p>
                </div>
              </div>
            ) : (
              <div>
                {elements.map((element) => (
                  <div
                    key={element.id}
                    className={`relative cursor-pointer ${
                      selectedElement === element.id ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => setSelectedElement(element.id)}
                  >
                    {renderElement(element)}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
