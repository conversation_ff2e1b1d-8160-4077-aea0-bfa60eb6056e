import { Module, Global } from '@nestjs/common';
import { LoggerService } from './services/logger.service';
import { ValidationService } from './services/validation.service';
import { CryptoService } from './services/crypto.service';
import { FileUploadService } from './services/file-upload.service';

@Global()
@Module({
  providers: [
    LoggerService,
    ValidationService,
    CryptoService,
    FileUploadService,
  ],
  exports: [
    LoggerService,
    ValidationService,
    CryptoService,
    FileUploadService,
  ],
})
export class CommonModule {}
