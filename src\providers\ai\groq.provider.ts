import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import {
  BaseAIProvider,
  AICompletionRequest,
  AICompletionResponse,
  AIStreamChunk,
  AIProviderConfig,
  AIProviderUsage,
} from '../interfaces/ai-provider.interface';

@Injectable()
export class GroqProvider extends BaseAIProvider {
  readonly name = 'groq';
  private readonly logger = new Logger(GroqProvider.name);
  private client: AxiosInstance | null = null;
  readonly config: AIProviderConfig;

  constructor(private readonly configService: ConfigService) {
    super();

    const apiKey = this.configService?.get<string>('GROQ_API_KEY') || process.env['GROQ_API_KEY'];

    this.config = {
      name: this.name,
      apiKey,
      baseUrl: 'https://api.groq.com/openai/v1',
      models: [
        'llama3-8b-8192',
        'llama3-70b-8192',
        'mixtral-8x7b-32768',
        'gemma-7b-it',
      ],
      maxTokens: 32768,
      supportsStreaming: true,
      supportsFunctions: false, // Groq doesn't support function calling yet
      isAvailable: !!apiKey,
    };

    if (this.isAvailable()) {
      this.client = axios.create({
        baseURL: this.config.baseUrl,
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        timeout: 60000,
      });
      this.logger.log('✅ Groq provider initialized successfully');
    } else {
      this.logger.warn('⚠️ Groq provider not available - missing API key');
    }
  }

  isAvailable(): boolean {
    return this.config.isAvailable;
  }

  getModels(): string[] {
    return this.config.models;
  }

  validateConfig(): boolean {
    return !!this.config.apiKey;
  }

  async createCompletion(request: AICompletionRequest): Promise<AICompletionResponse> {
    if (!this.client) {
      throw new Error('Groq client not initialized - missing API key');
    }

    try {
      const response = await this.client.post('/chat/completions', {
        model: request.model || 'llama3-8b-8192',
        messages: this.formatMessages(request.messages),
        temperature: request.temperature || 0.7,
        max_tokens: Math.min(request.max_tokens || 1024, this.config.maxTokens),
        top_p: request.top_p,
        stop: request.stop,
        stream: false,
      });

      return {
        id: response.data.id,
        object: response.data.object,
        created: response.data.created,
        model: response.data.model,
        choices: response.data.choices.map((choice: any) => ({
          index: choice.index,
          message: {
            role: choice.message.role,
            content: choice.message.content || '',
          },
          finish_reason: choice.finish_reason || '',
        })),
        usage: {
          prompt_tokens: response.data.usage?.prompt_tokens || 0,
          completion_tokens: response.data.usage?.completion_tokens || 0,
          total_tokens: response.data.usage?.total_tokens || 0,
        },
      };
    } catch (error) {
      this.handleError(error, 'completion failed');
    }
  }

  async *createStreamCompletion(request: AICompletionRequest): AsyncIterable<AIStreamChunk> {
    if (!this.client) {
      throw new Error('Groq client not initialized - missing API key');
    }

    try {
      const response = await this.client.post('/chat/completions', {
        model: request.model || 'llama3-8b-8192',
        messages: this.formatMessages(request.messages),
        temperature: request.temperature || 0.7,
        max_tokens: Math.min(request.max_tokens || 1024, this.config.maxTokens),
        top_p: request.top_p,
        stop: request.stop,
        stream: true,
      }, {
        responseType: 'stream',
      });

      let buffer = '';

      for await (const chunk of response.data) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              return;
            }

            try {
              const parsed = JSON.parse(data);
              yield {
                id: parsed.id,
                object: parsed.object,
                created: parsed.created,
                model: parsed.model,
                choices: parsed.choices.map((choice: any) => ({
                  index: choice.index,
                  delta: {
                    role: choice.delta?.role,
                    content: choice.delta?.content,
                  },
                  finish_reason: choice.finish_reason,
                })),
              };
            } catch (parseError) {
              // Skip invalid JSON chunks
              continue;
            }
          }
        }
      }
    } catch (error) {
      this.handleError(error, 'stream completion failed');
    }
  }

  calculateCost(usage: AIProviderUsage, model: string): number {
    // Groq pricing (as of 2024) - very competitive rates
    const pricing: Record<string, { input: number; output: number }> = {
      'llama3-8b-8192': { input: 0.0001, output: 0.0001 },
      'llama3-70b-8192': { input: 0.0008, output: 0.0008 },
      'mixtral-8x7b-32768': { input: 0.0006, output: 0.0006 },
      'gemma-7b-it': { input: 0.0001, output: 0.0001 },
    };

    const modelPricing = pricing[model] || pricing['llama3-8b-8192'];
    const inputCost = (usage.promptTokens / 1000) * modelPricing.input;
    const outputCost = (usage.completionTokens / 1000) * modelPricing.output;

    return inputCost + outputCost;
  }

  async testConnection(): Promise<boolean> {
    if (!this.client) {
      return false;
    }

    try {
      await this.client.get('/models');
      return true;
    } catch (error) {
      this.logger.error('Groq connection test failed:', error);
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    if (!this.client) {
      return this.config.models;
    }

    try {
      const response = await this.client.get('/models');
      return response.data.data
        .map((model: any) => model.id)
        .filter((id: string) => this.config.models.includes(id))
        .sort();
    } catch (error) {
      this.logger.warn('Failed to fetch Groq models, using defaults:', error);
      return this.config.models;
    }
  }
}
