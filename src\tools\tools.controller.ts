import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ToolsService } from '@/tools/tools.service';

@ApiTags('Tools')
@Controller('tools')
export class ToolsController {
  constructor(private readonly toolsService: ToolsService) {}

  @Get()
  @ApiOperation({ summary: 'Get available tools' })
  @ApiResponse({
    status: 200,
    description: 'Tools retrieved successfully',
  })
  async getTools() {
    return this.toolsService.getTools();
  }
}
