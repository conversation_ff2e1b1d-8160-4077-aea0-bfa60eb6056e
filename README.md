# <PERSON>ynapseAI Backend

Visual AI Agent Builder Platform - Production-ready backend API built with NestJS and TypeScript.

## 🚀 Features

- **Visual Agent Builder**: Drag-drop interface for creating AI agents
- **Multi-Provider AI**: Support for OpenAI, <PERSON>, <PERSON>, and more
- **Real-time Communication**: WebSocket-based APIX protocol
- **Authentication**: Multi-provider OAuth, 2FA, magic links
- **Payment Processing**: Stripe integration for subscriptions
- **File Management**: Image processing and optimization
- **Analytics**: Comprehensive tracking and reporting
- **Production-Ready**: Docker, monitoring, security, and scalability

## 🛠️ Tech Stack

- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Cache**: Redis for sessions and real-time data
- **Authentication**: JWT with Passport.js
- **File Processing**: Sharp for image optimization
- **Payment**: Stripe for billing and subscriptions
- **AI**: OpenAI, Anthropic, Google AI APIs
- **Testing**: Jest with comprehensive test coverage
- **Documentation**: Swagger/OpenAPI

## 📋 Prerequisites

- Node.js 18+ and npm 9+
- PostgreSQL 14+
- Redis 6+
- Docker (optional)

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd synapseai-backend
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
# Edit .env with your configuration
```

### 3. Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# (Optional) Open Prisma Studio
npm run db:studio
```

### 4. Start Development Server

```bash
npm run dev
```

The API will be available at `http://localhost:3001/api/v1`

## 📚 API Documentation

When running in development mode, Swagger documentation is available at:
`http://localhost:3001/api/v1/docs`

## 🔧 Available Scripts

### Development
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run start` - Start production server

### Database
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema changes to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

### Testing
- `npm run test` - Run unit tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report

### Code Quality
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run typecheck` - Run TypeScript type checking

### Docker
- `npm run docker:build` - Build Docker image
- `npm run docker:run` - Run Docker container

## 🏗️ Project Structure

```
src/
├── auth/              # Authentication module
├── users/             # User management
├── agents/            # AI agent system
├── tools/             # Tool integration
├── providers/         # AI provider management
├── landing/           # Landing page builder
├── payments/          # Payment processing
├── analytics/         # Analytics and tracking
├── email/             # Email service
├── websocket/         # Real-time communication
├── common/            # Shared utilities
├── config/            # Configuration
├── database/          # Database services
└── main.ts           # Application entry point
```

## 🔐 Environment Variables

### Required Variables

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/synapseai"
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your-super-secure-jwt-secret-key-here"
JWT_REFRESH_SECRET="your-super-secure-refresh-secret-key-here"

# AI Providers
OPENAI_API_KEY="sk-your-openai-api-key"

# Payment
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"
STRIPE_WEBHOOK_SECRET="whsec_your-webhook-secret"

# Email
SMTP_HOST="smtp.gmail.com"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
```

See `.env.example` for all available configuration options.

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Structure

- Unit tests: `*.spec.ts` files alongside source code
- Integration tests: `test/` directory
- Test utilities: `test/setup.ts`

## 🐳 Docker Deployment

### Build and Run

```bash
# Build image
docker build -t synapseai-backend .

# Run container
docker run -p 3001:3001 --env-file .env synapseai-backend
```

### Docker Compose

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
    depends_on:
      - postgres
      - redis
```

## 📊 Monitoring and Health Checks

### Health Endpoints

- `GET /api/v1/health` - Overall health status
- `GET /api/v1/health/ready` - Readiness check
- `GET /api/v1/health/live` - Liveness check

### Logging

Structured JSON logging with different levels:
- `error` - Error conditions
- `warn` - Warning conditions
- `info` - Informational messages
- `debug` - Debug information
- `verbose` - Verbose debug information

## 🔒 Security Features

- **Authentication**: JWT with refresh tokens
- **Authorization**: Role-based access control
- **Rate Limiting**: Configurable request limits
- **Input Validation**: Comprehensive request validation
- **CORS**: Configurable cross-origin policies
- **Helmet**: Security headers
- **Encryption**: Sensitive data encryption

## 🚀 Production Deployment

### Environment Setup

1. Set `NODE_ENV=production`
2. Configure production database
3. Set up Redis cluster
4. Configure monitoring and logging
5. Set up SSL/TLS certificates

### Performance Optimization

- Connection pooling for database
- Redis caching for sessions
- Image optimization with Sharp
- Compression middleware
- CDN for static assets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the API documentation at `/api/v1/docs`
