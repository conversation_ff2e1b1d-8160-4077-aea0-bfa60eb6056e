import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Navigation */}
      <nav className="border-b bg-white/80 backdrop-blur-sm dark:bg-slate-900/80">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/" className="text-2xl font-bold text-slate-900 dark:text-white">
                SynapseAI
              </Link>
              <div className="hidden md:flex space-x-6">
                <Link href="/dashboard" className="text-slate-600 hover:text-slate-900">Dashboard</Link>
                <Link href="/agents" className="text-slate-600 hover:text-slate-900">Agents</Link>
                <Link href="/builder" className="text-slate-600 hover:text-slate-900">Page Builder</Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost">Sign In</Button>
              <Link href="/dashboard">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          <h1 className="text-4xl sm:text-6xl font-bold text-slate-900 dark:text-white mb-6">
            Build Intelligent
            <span className="text-blue-600 dark:text-blue-400"> AI Agents </span>
            Visually
          </h1>
          <p className="text-xl text-slate-600 dark:text-slate-300 mb-8 max-w-3xl mx-auto">
            Create powerful AI automations with our drag-and-drop interface.
            No coding required. Deploy intelligent agents that work 24/7.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/dashboard">
              <Button size="lg" className="text-lg px-8 py-3">
                Start Building Free
              </Button>
            </Link>
            <Link href="/builder">
              <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                Try Page Builder
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-sm border">
            <h3 className="text-xl font-semibold mb-3">Visual Agent Builder</h3>
            <p className="text-slate-600 dark:text-slate-300">
              Drag and drop components to create complex AI workflows without writing code.
            </p>
          </div>
          <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-sm border">
            <h3 className="text-xl font-semibold mb-3">Multi-Provider AI</h3>
            <p className="text-slate-600 dark:text-slate-300">
              Connect to OpenAI, Groq, OpenRouter, and more. Switch providers seamlessly.
            </p>
          </div>
          <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-sm border">
            <h3 className="text-xl font-semibold mb-3">Real-time Deployment</h3>
            <p className="text-slate-600 dark:text-slate-300">
              Deploy your agents instantly and monitor their performance in real-time.
            </p>
          </div>
        </div>
      </main>
    </div>
  )
}
