import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService, HealthStatus } from './health.service';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @ApiOperation({ summary: 'Get application health status' })
  @ApiResponse({
    status: 200,
    description: 'Health status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            status: { type: 'string', example: 'healthy' },
            timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
            uptime: { type: 'number', example: 12345 },
            version: { type: 'string', example: '1.0.0' },
            environment: { type: 'string', example: 'development' },
            services: {
              type: 'object',
              properties: {
                database: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', example: 'healthy' },
                    responseTime: { type: 'number', example: 10 },
                  },
                },
                redis: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', example: 'healthy' },
                    responseTime: { type: 'number', example: 5 },
                  },
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 503,
    description: 'Service unavailable',
  })
  async getHealth(): Promise<{ success: boolean; data: HealthStatus }> {
    const healthStatus = await this.healthService.getHealthStatus();
    return {
      success: true,
      data: healthStatus,
    };
  }

  @Get('ready')
  @ApiOperation({ summary: 'Check if application is ready to serve requests' })
  @ApiResponse({
    status: 200,
    description: 'Application is ready',
  })
  @ApiResponse({
    status: 503,
    description: 'Application is not ready',
  })
  async getReadiness(): Promise<{ success: boolean; data: { ready: boolean } }> {
    const isReady = await this.healthService.isReady();
    return {
      success: true,
      data: { ready: isReady },
    };
  }

  @Get('live')
  @ApiOperation({ summary: 'Check if application is alive' })
  @ApiResponse({
    status: 200,
    description: 'Application is alive',
  })
  async getLiveness(): Promise<{ success: boolean; data: { alive: boolean } }> {
    const isAlive = this.healthService.isAlive();
    return {
      success: true,
      data: { alive: isAlive },
    };
  }
}
