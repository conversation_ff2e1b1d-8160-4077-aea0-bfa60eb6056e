{"semi": true, "trailingComma": "all", "singleQuote": true, "printWidth": 120, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "overrides": [{"files": "*.json", "options": {"printWidth": 80}}, {"files": "*.md", "options": {"proseWrap": "always", "printWidth": 80}}]}