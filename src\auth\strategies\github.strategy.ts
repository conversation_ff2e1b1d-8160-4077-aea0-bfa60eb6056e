import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-github2';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GithubStrategy extends PassportStrategy(Strategy, 'github') {
  constructor(private readonly configService: ConfigService) {
    super({
      clientID: configService?.get<string>('GITHUB_CLIENT_ID') || process.env['GITHUB_CLIENT_ID'] || '',
      clientSecret: configService?.get<string>('GITHUB_CLIENT_SECRET') || process.env['GITHUB_CLIENT_SECRET'] || '',
      callbackURL: '/api/v1/auth/github/callback',
      scope: ['user:email'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: (error: any, user?: any) => void,
  ): Promise<any> {
    const { username, displayName, emails, photos } = profile;

    const user = {
      email: emails[0].value,
      username,
      displayName,
      picture: photos[0].value,
      accessToken,
      refreshToken,
    };

    done(null, user);
  }
}
