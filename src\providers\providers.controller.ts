import { Controller, Get, Post, Body, Param, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ProvidersService } from '@/providers/providers.service';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';
import { TestProviderDto } from '@/providers/dto/providers.dto';

@ApiTags('Providers')
@Controller('providers')
export class ProvidersController {
    constructor(private readonly providersService: ProvidersService) { }

    @Get()
    @ApiOperation({ summary: 'Get available AI providers' })
    @ApiResponse({
        status: 200,
        description: 'Available providers retrieved successfully',
    })
    async getProviders(): Promise<any> {
        return this.providersService.getProviders();
    }

    @Get('models')
    @ApiOperation({ summary: 'Get all available models from all providers' })
    @ApiResponse({
        status: 200,
        description: 'Available models retrieved successfully',
    })
    async getModels(): Promise<any> {
        return this.providersService.getProviders();
    }

    @Get(':provider/models')
    @ApiOperation({ summary: 'Get models for a specific provider' })
    @ApiResponse({
        status: 200,
        description: 'Provider models retrieved successfully',
    })
        async getProviderModels(@Param('provider') provider: string): Promise<any> {
        return this.providersService.getProviders();
    }

    @Post('test')
    @ApiOperation({ summary: 'Test provider connections' })
    @ApiResponse({
        status: 200,
        description: 'Provider connections tested successfully',
    })
    async testConnections(): Promise<any> {
        return this.providersService.getProviders();
    }

    @Post(':provider/test')
    @ApiOperation({ summary: 'Test specific provider connection' })
    @ApiResponse({
        status: 200,
        description: 'Provider connection tested successfully',
    })
    async testProvider(@Param('provider') provider: string, @Body() testDto: TestProviderDto): Promise<any> {
        return this.providersService.testProvider(provider, testDto);
    }

    @Post('chat/completions')
    @UseGuards(JwtAuthGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({ summary: 'Create chat completion using any available provider' })
    @ApiResponse({
        status: 200,
        description: 'Chat completion created successfully',
    })
    async createChatCompletion(@Body() chatDto: any): Promise<any> {
        return this.providersService.createChatCompletion(chatDto);
    }

    @Get('usage/stats')
    @UseGuards(JwtAuthGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({ summary: 'Get provider usage statistics' })
    @ApiResponse({
        status: 200,
        description: 'Usage statistics retrieved successfully',
    })
    async getUsageStats(@Query('days') days?: number): Promise<any> {
        return this.providersService.getUsageStats(days);
    }
}