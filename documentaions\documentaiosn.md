SynapseAI: Comprehensive AI Build Prompt
Overall Goal
Develop the SynapseAI application, a cutting-edge AI agent solution that delivers a "wow" factor, effortless user experience, and seamless integration for all user categories (non-technical, technical, students, business small/big). The application will leverage a robust backend (UAUI Core Engine) and communicate transparently with user interfaces via the APIX Protocol.

Application Vision & Core Values
SynapseAI: An intelligent connection point orchestrating diverse AI models and user interactions.

Core Values: Problem Solver, Time Saver, Seamless Use, Easy to Use, Easy to Integrate/Plugin.

Transparency: Users should always understand what the AI is doing.

Interactivity: Enable rich, bidirectional human-in-the-loop (HITL) workflows.

Extensibility: Designed for easy addition of future features and integrations.

Target Audience
The solution must cater to:

Non-Technical Users: Intuitive, clear, effortless interaction.

Technical Users (Developers): Easy integration, clear APIs, comprehensive SDKs.

Students: Accessible and understandable for learning.

Small/Large Businesses: Scalable, reliable, and effective for automation and insights.

Locked Technology Stack
Frontend: Next.js (React with TypeScript) + Tailwind CSS

Backend: Python (FastAPI) + Uvicorn

Communication Protocol: APIX Protocol (custom defined, with provided specification)

APIX SDKs: apix-py-agent (Python) and @apix/client (JavaScript/TypeScript)

Core Components to Build
The development must cover all components as defined in the SynapseAI: Architecture Document:

Frontend (Client Application): Next.js App, React Components, @apix/client SDK Integration, Local UI State Management, Tailwind CSS Styling.

Backend Server: FastAPI Web Server (with WebSocket endpoint), UAUI Core Engine, APIX Integration Layer (APIX Event Publisher, APIX Event Handler).

External AI Providers: Integration with OpenAI, Claude, Gemini, Mistral, Groq.

Data Storage: Consideration for Session State (e.g., Redis) and Persistent Data (e.g., PostgreSQL/Firestore).

Detailed Requirements Implementation
Implement ALL Functional Requirements (FR) and adhere to ALL Non-Functional Requirements (NFR) as specified in the SynapseAI: Requirements Specification document.

Key Functional Requirements (FRs) to ensure are explicitly handled:

FR1.1 Intelligent AI Provider Selection: Implement the core logic for selecting the best LLM based on criteria (cost, speed, capability).

FR1.2 Multi-Provider Integration: Ensure working adapters for OpenAI, Claude, Gemini, Mistral, Groq.

FR1.3 Context Management: Design and implement session-specific context handling.

FR1.4 Tool Orchestration: Enable agent to invoke and manage external tools.

FR2.1 Real-time Text Streaming: Implement token-by-token streaming from backend to frontend.

FR2.2 Agent Status Transparency: Implement thinking_status events.

FR2.3 Tool Call Visibility & FR2.4 Tool Result/Error Reporting: Implement tool_call_start and tool_call_result events.

FR2.5 UI State Synchronization: Implement state_update events for granular UI updates.

FR2.6 Human-in-the-Loop (HITL) Input Request & FR2.8 User Response Handling: Implement request_user_input and user_response events.

FR2.9 Control Signal Processing: Implement "cancel" and "reset_session" functionality.

FR2.10 Error Reporting to UI: Implement error event emission.

FR3.1 Interactive Chat Interface & FR3.2 Real-time UI Updates: Ensure the frontend dynamically reacts to all APIX events.

FR3.3 Dynamic Input Controls: Frontend must render appropriate UI for HITL requests.

FR4.1 APIX Protocol Adherence: Strict adherence to the APIX Protocol specification is mandatory for all communication.

FR4.2 SDK Provision: The generated code should implicitly form the basis for the SDKs, demonstrating how they would be used.

FR4.3 Third-Party App Integration: Design choices must facilitate easy integration.

Key Non-Functional Requirements (NFRs) to ensure are explicitly addressed in implementation:

NFR1.1-NFR1.4 (Performance & Scalability): Leverage asyncio in Python, FastAPI's performance, Next.js optimizations, and design for horizontal scaling (Docker).

NFR2.1-NFR2.3 (Reliability & Availability): Implement robust error handling, graceful degradation, and client-side reconnection logic.

NFR3.1-NFR3.5 (Security): Implement secure communication (WSS), authentication/authorization, rigorous input validation, rate limiting, and secure handling of sensitive data.

NFR4.1-NFR4.4 (Usability): Prioritize intuitive UI, clear feedback, responsive design, and accessibility.

NFR5.1-NFR5.4 (Maintainability & Extensibility): Ensure modular code, high code quality, and a design that supports future feature additions.

Architectural Directives
Adhere strictly to the architectural design and principles outlined in the SynapseAI: Architecture Document:

High-Level Architecture: Implement the client-server model with WebSocket communication.

Component Responsibilities: Ensure clear separation of concerns for Frontend, Backend (FastAPI, UAUI Core, APIX Integration Layer), and External AI Providers.

Architectural Principles: Design and implement with Modularity, Scalability, Real-time & Event-Driven, Transparency, Extensibility, Developer Experience (DX), and Cloud Native principles as guiding forces.

Data Storage: Include conceptual placeholders or basic implementations for session state (e.g., Redis client integration) and persistent data (e.g., PostgreSQL ORM setup) as per the architecture document.

Data Flow Directives
Implement ALL detailed data flow steps as specified in the SynapseAI: Data Flow Diagram & Specification document.

User Initiates Interaction: Implement the flow from user_message creation to backend reception.

Backend Processes User Request: Implement the full cycle: user_message -> UAUI intake -> thinking_status -> AI Provider Selection -> Tool Orchestration -> tool_call_start -> External Tool Call -> tool_call_result -> LLM Interaction -> text_chunk streaming -> thinking_status: "end".

Human-in-the-Loop (HITL) Data Flow: Implement request_user_input from backend and user_response from frontend.

State Synchronization Data Flow: Implement state_update from backend and frontend reaction.

Data Storage Flow: Ensure data is handled (read/write) according to the conceptual storage flows.

User Flow Directives
Implement ALL core user flows as specified in the SynapseAI: User Flow Document document, ensuring the UI/UX aligns with the "effortless" and "transparent" goals for each:

Flow 1: Basic Conversational Interaction (Problem Solving): Full chat functionality with streaming and status.

Flow 2: Tool-Assisted Task Completion (Time Saver): Clear visualization of tool calls and results.

Flow 3: Human-in-the-Loop (HITL) Decision Making: Interactive prompts and user responses.

Flow 4: Integrating SynapseAI into a Third-Party Application (Developer Flow): The generated code should serve as the foundation for the SDK examples and integration guides.

Flow 5: Resetting the Chat Session: Implement clear reset functionality.

APIX Protocol Specification (Core Communication Contract)
Strictly adhere to the APIX - Agent-User Interaction Protocol Specification. This is the definitive source for all event structures and communication patterns.

Message Envelope: Implement the exact schema:

{
  "protocolVersion": "string",
  "sessionId": "string",
  "eventId": "string",
  "timestamp": "string",
  "event": {
    "type": "string",
    "payload": "object"
  }
}

Agent-to-UI Events (Output Stream): Implement all events with their precise schemas and purposes:

text_chunk: For streaming text.

{ "content": "string", "is_final": "boolean" }

thinking_status: For agent's internal state.

{ "status": "string", "message": "string (optional)" }

tool_call_start: For indicating tool invocation.

{ "tool_name": "string", "tool_args": "object" }

tool_call_result: For tool outcome.

{ "tool_name": "string", "result": "any (optional)", "error": "object (optional)" }

state_update: For granular UI state updates (JSON Pointer/Patch).

{ "path": "string", "value": "any", "op": "string (optional)" }

request_user_input: For HITL requests.

{ "input_id": "string", "prompt": "string", "input_type": "string", "options": "array (optional)", "schema": "object (optional)" }

error: For backend errors.

{ "code": "string", "message": "string", "details": "object (optional)", "is_recoverable": "boolean" }

UI-to-Agent Events (Input Stream): Implement all events with their precise schemas and purposes:

user_message: For natural language input.

{ "text": "string", "metadata": "object (optional)" }

user_response: For HITL responses.

{ "input_id": "string", "value": "any", "cancellation": "boolean (optional)" }

control_signal: For operational commands.

{ "signal": "string", "details": "object (optional)" }

Lifecycle & Session Management: Implement session initiation, termination, and error handling as per the protocol.

SDK Usage Directives & Provided Code Examples
The implementation must directly leverage and demonstrate the usage of the apix-py-agent and @apix/client SDKs. Refer to the provided code examples for the structure and implementation patterns.

Backend (apix-py-agent): Use the APIXAgentPublisher class to emit Agent-to-UI events and implement logic to handle incoming UI-to-Agent events.

Reference Code: The concepts and simplified implementation in SynapseAI Demo Backend (Python FastAPI) should be used as a direct guide for backend APIX integration.

Frontend (@apix/client): Use the APIXClient class to establish connections, listen for Agent-to-UI events, and send UI-to-Agent events.

Reference Code: The React example in APIX JavaScript/TypeScript UI SDK Example (React) provides the exact structure and usage patterns for the frontend.

Development Directives & Tooling Considerations
The generated code and project structure should reflect the principles and practices associated with modern AI development tools and platforms, ensuring the application is robust, maintainable, and ready for advanced MLOps and cloud deployment.

Monorepo Codebase:

Strict separation of backend/ and frontend/ directories.

Within backend/, clearly separate uaui_core/ (AI logic, provider adapters, tool orchestration) from apix_integration/ (APIX Event Publisher/Handler).

Within frontend/, use a clear component structure for React.

Comprehensive Testing:

Include placeholder test files (e.g., test_*.py, *.test.ts) that indicate where unit, integration, and end-to-end tests would reside.

Tests should cover all functional requirements and critical data/user flows.

Cloud Readiness (Docker):

Provide Dockerfiles for both the backend and frontend, optimized for production deployment.

Ensure environment variables are used for configuration (API keys, database connections, etc.).

Logging & Monitoring:

Integrate basic logging in the backend (e.g., Python's logging module) that can be easily configured for cloud logging services.

Consider where performance metrics (e.g., LLM latency, tool execution time) would be captured, aligning with MLOps principles.

Authentication & Authorization:

Implement a basic JWT-based authentication flow for WebSocket connections.

Ensure secure handling of API keys for LLM providers (e.g., environment variables, secure secrets management).

Rapid Development & Deployment Concepts:

The code should be clean, readable, and follow best practices to enable rapid iteration and deployment. Focus on reusability of components and clear API definitions.

Documentation:

Include extensive inline comments explaining complex logic, APIX event handling, and UAUI core decisions.

The code structure should inherently support the modular documentation structure outlined in SynapseAI: Modular Documentation Structure.

Deliverables (Code & Structure)
The output should be a complete, runnable project structure containing:

synapseai-app/

backend/

src/

main.py (FastAPI app, WebSocket endpoint)

uaui_core/ (UAUI Core Engine logic, Smart Provider Selection, Tool Orchestration)

uaui_core/adapters/ (LLM provider integrations: openai.py, claude.py, gemini.py, mistral.py, groq.py - with real API calls)

uaui_core/tools/ (Example tool implementations: weather.py, search.py)

apix_integration/ (APIX Event Publisher, APIX Event Handler)

models/ (Pydantic models for internal data, APIX event schemas)

config.py (Environment variable loading)

tests/ (Placeholder for backend tests)

Dockerfile

requirements.txt (or pyproject.toml for Poetry/PDM)

frontend/

next.config.js

package.json

tailwind.config.js

tsconfig.json

src/

app/ (Next.js App Router structure)

components/ (React components: ChatWindow.tsx, MessageBubble.tsx, InputField.tsx, ToolCallDisplay.tsx, HitlForm.tsx, etc.)

hooks/ (useApiXClient.ts)

lib/apix_client.ts (Integration of @apix/client SDK)

styles/ (Global styles, Tailwind directives)

tests/ (Placeholder for frontend tests)

docs/ (Placeholder for modular documentation, e.g., api-spec/, sdk-docs/, user-guide/)

README.md (Top-level project README)

This prompt provides a comprehensive blueprint for developing SynapseAI, ensuring all specified requirements, architectural elements, data flows, user experiences, and protocol details are meticulously covered for an AI to build this application.