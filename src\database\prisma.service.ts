import { Injectable, OnModuleInit, OnM<PERSON>ule<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { PrismaClient } from "@prisma/client";
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);

  constructor(private readonly configService: ConfigService) {
    const databaseUrl = configService?.get<string>('DATABASE_URL') || process.env['DATABASE_URL'];
    const nodeEnv = configService?.get<string>('NODE_ENV') || process.env['NODE_ENV'] || 'development';

    super({
      datasources: {
        db: {
          url: databaseUrl,
        },
      },
      log: nodeEnv === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
      errorFormat: 'pretty',
    });

    // Add query logging middleware for development
    if (nodeEnv === 'development') {
      this['$use'](async (params: any, next: any) => {
        const before = Date.now();
        const result = await next(params);
        const after = Date.now();

        this.logger.debug(
          `Query ${params.model}.${params.action} took ${after - before}ms`,
          {
            model: params.model,
            action: params.action,
            duration: after - before,
          },
        );

        return result;
      });
    }

    // Add error handling middleware
    this['$use'](async (params: any, next: any) => {
      try {
        return await next(params);
      } catch (error) {
        this.logger.error(
          `Database error in ${params.model}.${params.action}`,
          error instanceof Error ? error.stack : String(error),
          {
            model: params.model,
            action: params.action,
            args: params.args,
          },
        );
        throw error;
      }
    });
  }

  async onModuleInit(): Promise<void> {
    try {
      await this.$connect();
      this.logger.log('✅ Database connected successfully');

      // Test the connection
      await this.$queryRaw`SELECT 1`;
      this.logger.log('✅ Database connection verified');
    } catch (error) {
      this.logger.error('❌ Failed to connect to database', error);
      throw error;
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      await this.$disconnect();
      this.logger.log('✅ Database disconnected successfully');
    } catch (error) {
      this.logger.error('❌ Error disconnecting from database', error);
    }
  }

  /**
   * Execute a transaction with automatic retry logic
   */
  async executeTransaction<T>(
    fn: (prisma: any) => Promise<T>,
    maxRetries = 3,
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.$transaction(fn);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        this.logger.warn(
          `Transaction attempt ${attempt}/${maxRetries} failed: ${lastError.message}`,
          {
            attempt,
            maxRetries,
            error: lastError.message,
          },
        );

        // Don't retry on certain errors
        if (this.isNonRetryableError(lastError)) {
          throw lastError;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  /**
   * Check if an error should not be retried
   */
  private isNonRetryableError(error: Error): boolean {
    const nonRetryableErrors = [
      'P2002', // Unique constraint violation
      'P2003', // Foreign key constraint violation
      'P2025', // Record not found
      'P2014', // Invalid relation
    ];

    return nonRetryableErrors.some(code => error.message.includes(code));
  }

  /**
   * Get database health information
   */
  async getHealthInfo(): Promise<{
    status: 'healthy' | 'unhealthy';
    responseTime: number;
    connections?: number;
  }> {
    const startTime = Date.now();

    try {
      await this.$queryRaw`SELECT 1`;
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Soft delete helper
   */
  async softDelete(model: string, where: Record<string, unknown>): Promise<unknown> {
    const modelDelegate = (this as any)[model];
    if (!modelDelegate) {
      throw new Error(`Model ${model} not found`);
    }

    return modelDelegate.update({
      where,
      data: {
        deletedAt: new Date(),
      },
    });
  }

  /**
   * Restore soft deleted record
   */
  async restore(model: string, where: Record<string, unknown>): Promise<unknown> {
    const modelDelegate = (this as any)[model];
    if (!modelDelegate) {
      throw new Error(`Model ${model} not found`);
    }

    return modelDelegate.update({
      where,
      data: {
        deletedAt: null,
      },
    });
  }

  /**
   * Find many with soft delete filter
   */
  async findManyNotDeleted(
    model: string,
    args: Record<string, unknown> = {},
  ): Promise<unknown[]> {
    const modelDelegate = (this as any)[model];
    if (!modelDelegate) {
      throw new Error(`Model ${model} not found`);
    }

    return modelDelegate.findMany({
      ...args,
      where: {
        ...((args['where'] as Record<string, unknown>) || {}),
        deletedAt: null,
      },
    });
  }

  /**
   * Count records excluding soft deleted
   */
  async countNotDeleted(
    model: string,
    where: Record<string, unknown> = {},
  ): Promise<number> {
    const modelDelegate = (this as any)[model];
    if (!modelDelegate) {
      throw new Error(`Model ${model} not found`);
    }

    return modelDelegate.count({
      where: {
        ...where,
        deletedAt: null,
      },
    });
  }
}
