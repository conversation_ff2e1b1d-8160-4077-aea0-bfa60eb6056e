export interface AIMessage {
  role: 'system' | 'user' | 'assistant' | 'function';
  content: string;
  name?: string;
  function_call?: {
    name: string;
    arguments: string;
  };
}

export interface AICompletionRequest {
  messages: AIMessage[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
  stream?: boolean;
  functions?: AIFunction[];
  function_call?: 'auto' | 'none' | { name: string };
}

export interface AIFunction {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, {
      type: string;
      description: string;
      enum?: string[];
    }>;
    required?: string[];
  };
}

export interface AICompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: AIMessage;
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface AIStreamChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: {
      role?: string;
      content?: string;
      function_call?: {
        name?: string;
        arguments?: string;
      };
    };
    finish_reason?: string;
  }>;
}

export interface AIProviderConfig {
  name: string;
  apiKey?: string;
  baseUrl?: string;
  models: string[];
  maxTokens: number;
  supportsStreaming: boolean;
  supportsFunctions: boolean;
  isAvailable: boolean;
}

export interface AIProviderUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  cost?: number;
}

export abstract class BaseAIProvider {
  abstract readonly name: string;
  abstract readonly config: AIProviderConfig;

  abstract isAvailable(): boolean;
  abstract getModels(): string[];
  abstract validateConfig(): boolean;

  abstract createCompletion(
    request: AICompletionRequest,
  ): Promise<AICompletionResponse>;

  abstract createStreamCompletion(
    request: AICompletionRequest,
  ): AsyncIterable<AIStreamChunk>;

  abstract calculateCost(usage: AIProviderUsage, model: string): number;

  protected formatMessages(messages: AIMessage[]): any[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      ...(msg.name && { name: msg.name }),
      ...(msg.function_call && { function_call: msg.function_call }),
    }));
  }

  protected handleError(error: any, context: string): never {
    const errorMessage = error.response?.data?.error?.message || error.message || 'Unknown error';
    throw new Error(`${this.name} ${context}: ${errorMessage}`);
  }
}
