import { Injectable, UnauthorizedException, ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '@/database/prisma.service';
import { RedisService } from '@/database/redis.service';
import { CryptoService } from '@/common/services/crypto.service';
import { LoggerService } from '@/common/services/logger.service';
import { LoginDto, RegisterDto } from './dto/auth.dto';
import { AuthenticationException, ResourceNotFoundException } from '@/common/filters/global-exception.filter';

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
  };
  tokens: AuthTokens;
}

@Injectable()
export class AuthService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly redisService: RedisService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly cryptoService: CryptoService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Register a new user
   */
  async register(registerDto: RegisterDto): Promise<{ success: true; data: AuthResponse }> {
    const { email, password, name } = registerDto;

    // Check if user already exists
    const existingUser = await this.prismaService['user'].findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await this.cryptoService.hashPassword(password);

    // Create user
    const user = await this.prismaService['user'].create({
      data: {
        email,
        password: hashedPassword,
        name,
        emailVerified: false,
        isActive: true,
      },
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Generate tokens
    const tokens = await this.generateTokens(user.id, user.email);

    // Store refresh token
    await this.storeRefreshToken(user.id, tokens.refreshToken);

    // Log user registration
    this.loggerService.logUserAction(user.id, 'user_registered', {
      email: user.email,
      name: user.name,
    });

    return {
      success: true,
      data: {
        user,
        tokens,
      },
    };
  }

  /**
   * Login user
   */
  async login(loginDto: LoginDto): Promise<{ success: true; data: AuthResponse }> {
    const { email, password } = loginDto;

    // Find user
    const user = await this.prismaService['user'].findUnique({
      where: { email },
    });

    if (!user) {
      throw new AuthenticationException('Invalid email or password');
    }

    // Verify password
    const isPasswordValid = await this.cryptoService.verifyPassword(password, user.password);

    if (!isPasswordValid) {
      this.loggerService.logSecurityEvent('failed_login_attempt', {
        email,
        ip: 'unknown', // This would come from request context
      });
      throw new AuthenticationException('Invalid email or password');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new AuthenticationException('Account is deactivated');
    }

    // Generate tokens
    const tokens = await this.generateTokens(user.id, user.email);

    // Store refresh token
    await this.storeRefreshToken(user.id, tokens.refreshToken);

    // Update last login
    await this.prismaService['user'].update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // Log successful login
    this.loggerService.logUserAction(user.id, 'user_logged_in', {
      email: user.email,
    });

    const userResponse = {
      id: user.id,
      email: user.email,
      name: user.name,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    return {
      success: true,
      data: {
        user: userResponse,
        tokens,
      },
    };
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<{ success: true; data: AuthTokens }> {
    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET') || '',
      });

      // Check if refresh token exists in Redis
      const storedToken = await this.redisService.get(`refresh_token:${payload.sub}`);
      if (!storedToken || storedToken !== refreshToken) {
        throw new AuthenticationException('Invalid refresh token');
      }

      // Find user
      const user = await this.prismaService['user'].findUnique({
        where: { id: payload.sub },
      });

      if (!user || !user.isActive) {
        throw new AuthenticationException('User not found or inactive');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user.id, user.email);

      // Store new refresh token
      await this.storeRefreshToken(user.id, tokens.refreshToken);

      return {
        success: true,
        data: tokens,
      };
    } catch (error) {
      throw new AuthenticationException('Invalid refresh token');
    }
  }

  /**
   * Logout user
   */
  async logout(userId: string): Promise<{ success: true; data: { message: string } }> {
    // Remove refresh token from Redis
    await this.redisService.del(`refresh_token:${userId}`);

    // Log logout
    this.loggerService.logUserAction(userId, 'user_logged_out');

    return {
      success: true,
      data: {
        message: 'Logged out successfully',
      },
    };
  }

  /**
   * Get user profile
   */
  async getProfile(userId: string): Promise<{ success: true; data: any }> {
    const user = await this.prismaService['user'].findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      throw new ResourceNotFoundException('User', userId);
    }

    return {
      success: true,
      data: user,
    };
  }

  /**
   * Validate user by ID (used by JWT strategy)
   */
  async validateUser(userId: string): Promise<any> {
    const user = await this.prismaService['user'].findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        isActive: true,
      },
    });

    if (!user || !user.isActive) {
      return null;
    }

    return user;
  }

  /**
   * Generate JWT tokens
   */
  private async generateTokens(userId: string, email: string): Promise<AuthTokens> {
    const payload = { sub: userId, email };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('JWT_SECRET') || '',
        expiresIn: this.configService.get<string>('JWT_EXPIRES_IN') || '15m',
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET') || '',
        expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN') || '7d',
      }),
    ]);

    return {
      accessToken,
      refreshToken,
    };
  }

  /**
   * Store refresh token in Redis
   */
  private async storeRefreshToken(userId: string, refreshToken: string): Promise<void> {
    const ttl = 7 * 24 * 60 * 60; // 7 days in seconds
    await this.redisService.set(`refresh_token:${userId}`, refreshToken, ttl);
  }
}
