import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { ThrottlerException } from '@nestjs/throttler';

export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: unknown;
    timestamp: string;
    path: string;
    method: string;
    statusCode: number;
    requestId?: string;
  };
}

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const errorResponse = this.buildErrorResponse(exception, request);

    // Log error details
    this.logError(exception, request, errorResponse);

    response.status(errorResponse.error.statusCode).json(errorResponse);
  }

  private buildErrorResponse(exception: unknown, request: Request): ErrorResponse {
    const timestamp = new Date().toISOString();
    const path = request.url;
    const method = request.method;
    const requestId = request.headers['x-request-id'] as string;

    // Handle HTTP exceptions
    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      return {
        success: false,
        error: {
          code: this.getErrorCode(status, exception),
          message: this.getErrorMessage(exceptionResponse),
          details: typeof exceptionResponse === 'object' ? exceptionResponse : undefined,
          timestamp,
          path,
          method,
          statusCode: status,
          requestId,
        },
      };
    }

    // Handle Prisma errors
    if (exception instanceof PrismaClientKnownRequestError) {
      return this.handlePrismaError(exception, timestamp, path, method, requestId);
    }

    // Handle Throttler exceptions
    if (exception instanceof ThrottlerException) {
      return {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests. Please try again later.',
          timestamp,
          path,
          method,
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          requestId,
        },
      };
    }

    // Handle validation errors
    if (this.isValidationError(exception)) {
      return this.handleValidationError(exception, timestamp, path, method, requestId);
    }

    // Handle unknown errors
    return {
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred. Please try again later.',
        timestamp,
        path,
        method,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        requestId,
      },
    };
  }

  private handlePrismaError(
      exception: PrismaClientKnownRequestError,
    timestamp: string,
    path: string,
    method: string,
    requestId?: string,
  ): ErrorResponse {
    let statusCode = HttpStatus.BAD_REQUEST;
    let code = 'DATABASE_ERROR';
    let message = 'Database operation failed';

    switch (exception.code) {
      case 'P2002':
        statusCode = HttpStatus.CONFLICT;
        code = 'UNIQUE_CONSTRAINT_VIOLATION';
        message = 'A record with this information already exists';
        break;
      case 'P2025':
        statusCode = HttpStatus.NOT_FOUND;
        code = 'RECORD_NOT_FOUND';
        message = 'The requested record was not found';
        break;
      case 'P2003':
        statusCode = HttpStatus.BAD_REQUEST;
        code = 'FOREIGN_KEY_CONSTRAINT_VIOLATION';
        message = 'Invalid reference to related record';
        break;
      case 'P2014':
        statusCode = HttpStatus.BAD_REQUEST;
        code = 'INVALID_RELATION';
        message = 'Invalid relation in the request';
        break;
      default:
        statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
        code = 'DATABASE_ERROR';
        message = 'Database operation failed';
    }

    return {
      success: false,
      error: {
        code,
        message,
        details: {
          prismaCode: exception.code,
          target: exception.meta?.['target'] as string,
        },
        timestamp,
        path,
        method,
        statusCode,
        requestId: requestId || '',
      },
    };
  }

  private handleValidationError(
    exception: unknown,
    timestamp: string,
    path: string,
    method: string,
    requestId?: string,
  ): ErrorResponse {
    return {
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Request validation failed',
        details: exception,
        timestamp,
        path,
        method,
        statusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        requestId: requestId || '',
      },
    };
  }

  private getErrorCode(status: number, exception: HttpException): string {
    const exceptionName = exception.constructor.name;

    switch (status) {
      case HttpStatus.BAD_REQUEST:
        return 'BAD_REQUEST';
      case HttpStatus.UNAUTHORIZED:
        return 'UNAUTHORIZED';
      case HttpStatus.FORBIDDEN:
        return 'FORBIDDEN';
      case HttpStatus.NOT_FOUND:
        return 'NOT_FOUND';
      case HttpStatus.CONFLICT:
        return 'CONFLICT';
      case HttpStatus.UNPROCESSABLE_ENTITY:
        return 'VALIDATION_ERROR';
      case HttpStatus.TOO_MANY_REQUESTS:
        return 'RATE_LIMIT_EXCEEDED';
      case HttpStatus.INTERNAL_SERVER_ERROR:
        return 'INTERNAL_SERVER_ERROR';
      default:
        return exceptionName.replace('Exception', '').toUpperCase();
    }
  }

  private getErrorMessage(exceptionResponse: string | object): string {
    if (typeof exceptionResponse === 'string') {
      return exceptionResponse;
    }

    if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
      const response = exceptionResponse as Record<string, unknown>;

      if (typeof response['message'] === 'string') {
        return response['message'];
      }

      if (Array.isArray(response['message'])) {
        return response['message'].join(', ');
      }
    }

    return 'An error occurred';
  }

  private isValidationError(exception: unknown): boolean {
    return (
      exception instanceof Error &&
      (exception.name === 'ValidationError' ||
        exception.message.includes('validation') ||
        exception.message.includes('invalid'))
    );
  }

  private logError(exception: unknown, request: Request, errorResponse: ErrorResponse): void {
    const { error } = errorResponse;
    const userAgent = request.headers['user-agent'] || 'Unknown';
    const ip = request.ip || 'Unknown';

    const logContext = {
      requestId: error.requestId,
      method: error.method,
      path: error.path,
      statusCode: error.statusCode,
      userAgent,
      ip,
      userId: (request as any).user?.id,
    };

    if (error.statusCode >= 500) {
      this.logger.error(
        `${error.code}: ${error.message}`,
        exception instanceof Error ? exception.stack : String(exception),
        logContext,
      );
    } else if (error.statusCode >= 400) {
      this.logger.warn(`${error.code}: ${error.message}`, logContext);
    }
  }
}

// Custom exception classes for better error handling
export class BusinessLogicException extends HttpException {
  constructor(message: string, code: string, statusCode = HttpStatus.BAD_REQUEST) {
    super(
      {
        code,
        message,
        timestamp: new Date().toISOString(),
      },
      statusCode,
    );
  }
}

export class ValidationException extends HttpException {
  constructor(message: string, details?: unknown) {
    super(
      {
        code: 'VALIDATION_ERROR',
        message,
        details,
        timestamp: new Date().toISOString(),
      },
      HttpStatus.UNPROCESSABLE_ENTITY,
    );
  }
}

export class AuthenticationException extends HttpException {
  constructor(message = 'Authentication failed') {
    super(
      {
        code: 'AUTHENTICATION_FAILED',
        message,
        timestamp: new Date().toISOString(),
      },
      HttpStatus.UNAUTHORIZED,
    );
  }
}

export class AuthorizationException extends HttpException {
  constructor(message = 'Access denied') {
    super(
      {
        code: 'ACCESS_DENIED',
        message,
        timestamp: new Date().toISOString(),
      },
      HttpStatus.FORBIDDEN,
    );
  }
}

export class ResourceNotFoundException extends HttpException {
  constructor(resource: string, identifier?: string) {
    const message = identifier
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;

    super(
      {
        code: 'RESOURCE_NOT_FOUND',
        message,
        timestamp: new Date().toISOString(),
      },
      HttpStatus.NOT_FOUND,
    );
  }
}
