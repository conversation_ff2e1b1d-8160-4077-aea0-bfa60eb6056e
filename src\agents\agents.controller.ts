import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards, Req, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AgentsService } from '@/agents/agents.service';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';
import { CreateAgentDto, UpdateAgentDto } from '@/agents/dto/agents.dto';
import { Request } from 'express';

@ApiTags('Agents')
@Controller('agents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class AgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new agent' })
  @ApiResponse({
    status: 201,
    description: 'Agent created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'uuid' },
            name: { type: 'string', example: 'Customer Support Agent' },
            description: { type: 'string', example: 'Handles customer inquiries' },
            flowData: { type: 'object' },
            createdAt: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  async createAgent(@Req() req: Request, @Body() createAgentDto: CreateAgentDto) {
    const user = (req as any).user;
    return this.agentsService.createAgent(user.id, createAgentDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get user agents' })
  @ApiResponse({
    status: 200,
    description: 'Agents retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'uuid' },
              name: { type: 'string', example: 'Customer Support Agent' },
              description: { type: 'string', example: 'Handles customer inquiries' },
              isActive: { type: 'boolean', example: true },
              createdAt: { type: 'string', format: 'date-time' },
            },
          },
        },
      },
    },
  })
  async getAgents(@Req() req: Request, @Query('page') page?: number, @Query('limit') limit?: number) {
    const user = (req as any).user;
    return this.agentsService.getAgents(user.id, page, limit);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get agent by ID' })
  @ApiResponse({
    status: 200,
    description: 'Agent retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'uuid' },
            name: { type: 'string', example: 'Customer Support Agent' },
            description: { type: 'string', example: 'Handles customer inquiries' },
            flowData: { type: 'object' },
            isActive: { type: 'boolean', example: true },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  async getAgent(@Req() req: Request, @Param('id') agentId: string) {
    const user = (req as any).user;
    return this.agentsService.getAgent(user.id, agentId);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update agent' })
  @ApiResponse({
    status: 200,
    description: 'Agent updated successfully',
  })
  async updateAgent(
    @Req() req: Request,
    @Param('id') agentId: string,
    @Body() updateAgentDto: UpdateAgentDto,
  ) {
    const user = (req as any).user;
    return this.agentsService.updateAgent(user.id, agentId, updateAgentDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete agent' })
  @ApiResponse({
    status: 200,
    description: 'Agent deleted successfully',
  })
  async deleteAgent(@Req() req: Request, @Param('id') agentId: string) {
    const user = (req as any).user;
    return this.agentsService.deleteAgent(user.id, agentId);
  }
}
