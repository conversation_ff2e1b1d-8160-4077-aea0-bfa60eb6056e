// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  password      String
  name          String
  avatar        String?
  emailVerified <PERSON>olean   @default(false)
  isActive      Boolean   @default(true)
  lastLoginAt   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  deletedAt     DateTime?

  // Relations
  agents Agent[]

  @@map("users")
}

model Agent {
  id          String    @id @default(cuid())
  name        String
  description String?
  flowData    Json      @default("{}")
  isActive    Boolean   @default(true)
  userId      String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("agents")
}

model Tool {
  id          String    @id @default(cuid())
  name        String
  description String?
  config      <PERSON><PERSON>      @default("{}")
  isBuiltin   Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  @@map("tools")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("sessions")
}

model ApiKey {
  id           String    @id @default(cuid())
  userId       String
  name         String
  keyHash      String    @unique
  lastUsedAt   DateTime?
  expiresAt    DateTime?
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?

  @@map("api_keys")
}
