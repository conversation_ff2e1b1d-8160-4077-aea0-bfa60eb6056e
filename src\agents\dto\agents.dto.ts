import { IsString, IsOptional, IsBoolean, IsObject, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAgentDto {
  @ApiProperty({
    description: 'Agent name',
    example: 'Customer Support Agent',
    minLength: 2,
    maxLength: 100,
  })
  @IsString({ message: 'Name must be a string' })
  @MinLength(2, { message: 'Name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Name must not exceed 100 characters' })
  name!: string;

  @ApiProperty({
    description: 'Agent description',
    example: 'Handles customer inquiries and support requests',
    required: false,
    maxLength: 500,
  })
  @IsString({ message: 'Description must be a string' })
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Agent flow configuration',
    example: {
      nodes: [
        {
          id: 'start',
          type: 'message',
          config: {
            prompt: 'Hello! How can I help you today?',
          },
        },
      ],
      connections: [],
    },
    required: false,
  })
  @IsObject({ message: 'Flow data must be an object' })
  @IsOptional()
  flowData?: {
    nodes: Array<{
      id: string;
      type: 'message' | 'tool' | 'condition' | 'human' | 'end';
      config: Record<string, unknown>;
    }>;
    connections: Array<{
      source: string;
      target: string;
      sourceHandle?: string;
      targetHandle?: string;
    }>;
  };
}

export class UpdateAgentDto {
  @ApiProperty({
    description: 'Agent name',
    example: 'Customer Support Agent',
    required: false,
    minLength: 2,
    maxLength: 100,
  })
  @IsString({ message: 'Name must be a string' })
  @MinLength(2, { message: 'Name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Name must not exceed 100 characters' })
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Agent description',
    example: 'Handles customer inquiries and support requests',
    required: false,
    maxLength: 500,
  })
  @IsString({ message: 'Description must be a string' })
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Agent flow configuration',
    example: {
      nodes: [
        {
          id: 'start',
          type: 'message',
          config: {
            prompt: 'Hello! How can I help you today?',
          },
        },
      ],
      connections: [],
    },
    required: false,
  })
  @IsObject({ message: 'Flow data must be an object' })
  @IsOptional()
  flowData?: {
    nodes: Array<{
      id: string;
      type: 'message' | 'tool' | 'condition' | 'human' | 'end';
      config: Record<string, unknown>;
    }>;
    connections: Array<{
      source: string;
      target: string;
      sourceHandle?: string;
      targetHandle?: string;
    }>;
  };

  @ApiProperty({
    description: 'Whether the agent is active',
    example: true,
    required: false,
  })
  @IsBoolean({ message: 'isActive must be a boolean' })
  @IsOptional()
  isActive?: boolean;
}
