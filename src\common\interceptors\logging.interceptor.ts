import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<unknown> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const startTime = Date.now();

    // Generate request ID if not present
    const requestId = (request.headers['x-request-id'] as string) || uuidv4();
    request.headers['x-request-id'] = requestId;
    response.setHeader('x-request-id', requestId);

    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || 'Unknown';
    const userId = (request as any).user?.id;

    const logContext = {
      requestId,
      method,
      url,
      ip,
      userAgent,
      userId,
    };

    // Log incoming request
    this.logger.log(`📥 ${method} ${url}`, logContext);

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        const { statusCode } = response;

        const responseLogContext = {
          ...logContext,
          statusCode,
          duration: `${duration}ms`,
        };

        // Log successful response
        if (statusCode < 400) {
          this.logger.log(`📤 ${method} ${url} - ${statusCode} (${duration}ms)`, responseLogContext);
        } else {
          this.logger.warn(`⚠️ ${method} ${url} - ${statusCode} (${duration}ms)`, responseLogContext);
        }

        // Log slow requests
        if (duration > 1000) {
          this.logger.warn(`🐌 Slow request detected: ${method} ${url} took ${duration}ms`, responseLogContext);
        }
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        const statusCode = error.status || 500;

        const errorLogContext = {
          ...logContext,
          statusCode,
          duration: `${duration}ms`,
          error: error.message,
        };

        this.logger.error(`❌ ${method} ${url} - ${statusCode} (${duration}ms)`, error.stack, errorLogContext);

        throw error;
      }),
    );
  }
}

// Request ID middleware to ensure all requests have a unique ID
export function requestIdMiddleware(req: Request, res: Response, next: () => void): void {
  const requestId = (req.headers['x-request-id'] as string) || uuidv4();
  req.headers['x-request-id'] = requestId;
  res.setHeader('x-request-id', requestId);
  next();
}
