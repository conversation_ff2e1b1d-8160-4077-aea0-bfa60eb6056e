import { Injectable, Logger } from '@nestjs/common';
import { OpenAIProvider } from './openai.provider';
import { GroqProvider } from './groq.provider';
import { OpenRouterProvider } from './openrouter.provider';
import { BaseAIProvider } from '../interfaces/ai-provider.interface';

export type ProviderName = 'openai' | 'groq' | 'openrouter';

@Injectable()
export class AIProviderFactory {
  private readonly logger = new Logger(AIProviderFactory.name);
  private readonly providers = new Map<ProviderName, BaseAIProvider>();

  constructor(
    private readonly openaiProvider: OpenAIProvider,
    private readonly groqProvider: GroqProvider,
    private readonly openrouterProvider: OpenRouterProvider,
  ) {
    this.providers.set('openai', this.openaiProvider);
    this.providers.set('groq', this.groqProvider);
    this.providers.set('openrouter', this.openrouterProvider);

    this.logProviderStatus();
  }

  /**
   * Get a specific provider by name
   */
  getProvider(name: ProviderName): BaseAIProvider {
    const provider = this.providers.get(name);
    if (!provider) {
      throw new Error(`Provider '${name}' not found`);
    }

    if (!provider.isAvailable()) {
      throw new Error(`Provider '${name}' is not available - missing API key`);
    }

    return provider;
  }

  /**
   * Get all available providers
   */
  getAvailableProviders(): BaseAIProvider[] {
    return Array.from(this.providers.values()).filter(provider => provider && provider.isAvailable());
  }

  /**
   * Get provider names that are available
   */
  getAvailableProviderNames(): ProviderName[] {
    return Array.from(this.providers.entries())
      .filter(([, provider]) => provider && provider.isAvailable())
      .map(([name]) => name);
  }

  /**
   * Get the best available provider (prioritized order)
   */
  getBestAvailableProvider(): BaseAIProvider {
    const priorityOrder: ProviderName[] = ['openai', 'groq', 'openrouter'];

    for (const providerName of priorityOrder) {
      const provider = this.providers.get(providerName);
      if (provider?.isAvailable()) {
        return provider;
      }
    }

    throw new Error('No AI providers are available - please configure at least one API key');
  }

  /**
   * Get provider by model name
   */
  getProviderForModel(model: string): BaseAIProvider {
    // Check each provider to see if it supports the model
    for (const provider of this.providers.values()) {
      if (provider.isAvailable() && provider.getModels().includes(model)) {
        return provider;
      }
    }

    // If no provider explicitly supports the model, try to infer from model name
    if (model.includes('gpt')) {
      return this.getProvider('openai');
    }

    if (model.includes('llama') || model.includes('mixtral') || model.includes('gemma')) {
      return this.getProvider('groq');
    }

    if (model.includes('claude') || model.includes('gemini') || model.includes('command')) {
      return this.getProvider('openrouter');
    }

    // Fallback to best available provider
    return this.getBestAvailableProvider();
  }

  /**
   * Get all available models from all providers
   */
  getAllAvailableModels(): Array<{ provider: string; model: string; displayName: string }> {
    const models: Array<{ provider: string; model: string; displayName: string }> = [];

    for (const [providerName, provider] of this.providers.entries()) {
      if (provider.isAvailable()) {
        for (const model of provider.getModels()) {
          models.push({
            provider: providerName,
            model,
            displayName: this.getModelDisplayName(model, providerName),
          });
        }
      }
    }

    return models.sort((a, b) => a.displayName.localeCompare(b.displayName));
  }

  /**
   * Test connection for all providers
   */
  async testAllConnections(): Promise<Record<ProviderName, boolean>> {
    const results: Record<string, boolean> = {};

    for (const [name, provider] of this.providers.entries()) {
      if (provider.isAvailable()) {
        try {
          // Check if provider has testConnection method
          if ('testConnection' in provider && typeof provider.testConnection === 'function') {
            results[name] = await (provider as any).testConnection();
          } else {
            // Fallback: assume available if configured
            results[name] = true;
          }
        } catch (error) {
          this.logger.error(`Connection test failed for ${name}:`, error);
          results[name] = false;
        }
      } else {
        results[name] = false;
      }
    }

    return results as Record<ProviderName, boolean>;
  }

  /**
   * Get provider statistics
   */
  getProviderStats(): Array<{
    name: string;
    isAvailable: boolean;
    modelCount: number;
    supportsStreaming: boolean;
    supportsFunctions: boolean;
    maxTokens: number;
  }> {
    return Array.from(this.providers.entries()).map(([name, provider]) => ({
      name,
      isAvailable: provider.isAvailable(),
      modelCount: provider.getModels().length,
      supportsStreaming: provider.config.supportsStreaming,
      supportsFunctions: provider.config.supportsFunctions,
      maxTokens: provider.config.maxTokens,
    }));
  }

  /**
   * Log provider status on startup
   */
  private logProviderStatus(): void {
    const availableProviders = this.getAvailableProviderNames();
    const totalProviders = this.providers.size;

    this.logger.log(`🤖 AI Providers Status: ${availableProviders.length}/${totalProviders} available`);

    for (const [name, provider] of this.providers.entries()) {
      if (provider) {
        const status = provider.isAvailable() ? '✅' : '❌';
        const modelCount = provider.getModels().length;
        this.logger.log(`   ${status} ${name}: ${modelCount} models`);
      }
    }

    if (availableProviders.length === 0) {
      this.logger.warn('⚠️ No AI providers are available! Please configure API keys.');
    }
  }

  /**
   * Get human-readable model display name
   */
  private getModelDisplayName(model: string, provider: string): string {
    const displayNames: Record<string, string> = {
      // OpenAI
      'gpt-4-turbo-preview': 'GPT-4 Turbo (OpenAI)',
      'gpt-4': 'GPT-4 (OpenAI)',
      'gpt-4-32k': 'GPT-4 32K (OpenAI)',
      'gpt-3.5-turbo': 'GPT-3.5 Turbo (OpenAI)',
      'gpt-3.5-turbo-16k': 'GPT-3.5 Turbo 16K (OpenAI)',

      // Groq
      'llama3-8b-8192': 'Llama 3 8B (Groq)',
      'llama3-70b-8192': 'Llama 3 70B (Groq)',
      'mixtral-8x7b-32768': 'Mixtral 8x7B (Groq)',
      'gemma-7b-it': 'Gemma 7B (Groq)',

      // OpenRouter
      'openai/gpt-4-turbo-preview': 'GPT-4 Turbo (OpenRouter)',
      'openai/gpt-4': 'GPT-4 (OpenRouter)',
      'openai/gpt-3.5-turbo': 'GPT-3.5 Turbo (OpenRouter)',
      'anthropic/claude-3-opus': 'Claude 3 Opus (OpenRouter)',
      'anthropic/claude-3-sonnet': 'Claude 3 Sonnet (OpenRouter)',
      'anthropic/claude-3-haiku': 'Claude 3 Haiku (OpenRouter)',
      'google/gemini-pro': 'Gemini Pro (OpenRouter)',
      'meta-llama/llama-3-70b-instruct': 'Llama 3 70B (OpenRouter)',
      'meta-llama/llama-3-8b-instruct': 'Llama 3 8B (OpenRouter)',
      'mistralai/mixtral-8x7b-instruct': 'Mixtral 8x7B (OpenRouter)',
      'cohere/command-r-plus': 'Command R+ (OpenRouter)',
    };

    return displayNames[model] || `${model} (${provider})`;
  }
}
