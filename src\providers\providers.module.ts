import { Module } from '@nestjs/common';
import { ProvidersController } from './providers.controller';
import { ProvidersService } from '@/providers/providers.service';
import { OpenAIProvider } from './ai/openai.provider';
import { GroqProvider } from './ai/groq.provider';
import { OpenRouterProvider } from './ai/openrouter.provider';
import { AIProviderFactory } from './ai/ai-provider.factory';

@Module({
    controllers: [ProvidersController],
    providers: [
        ProvidersService,
        OpenAIProvider,
        GroqProvider,
        OpenRouterProvider,
        AIProviderFactory,
    ],
    exports: [
        ProvidersService,
        AIProviderFactory,
    ],
})
export class ProvidersModule { }
