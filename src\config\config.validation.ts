import * as Jo<PERSON> from 'joi';

export const configValidationSchema = Joi.object({
  // Server Configuration
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test', 'staging')
    .default('development'),
  PORT: Joi.number().port().default(3001),
  API_PREFIX: Joi.string().default('api/v1'),
  CORS_ORIGIN: Joi.string().default('http://localhost:3000'),

  // Database Configuration
  DATABASE_URL: Joi.string().uri().optional(),
  REDIS_URL: Joi.string().uri().optional(),

  // JWT Configuration
  JWT_SECRET: Joi.string().min(32).optional(),
  JWT_REFRESH_SECRET: Joi.string().min(32).optional(),
  JWT_EXPIRES_IN: Joi.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: Joi.string().default('7d'),

  // OAuth Providers
  GOOGLE_CLIENT_ID: Joi.string().when('ENABLE_OAUTH', {
    is: true,
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  GOOGLE_CLIENT_SECRET: Joi.string().when('ENABLE_OAUTH', {
    is: true,
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  GITHUB_CLIENT_ID: Joi.string().when('ENABLE_OAUTH', {
    is: true,
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  GITHUB_CLIENT_SECRET: Joi.string().when('ENABLE_OAUTH', {
    is: true,
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  MICROSOFT_CLIENT_ID: Joi.string().optional(),
  MICROSOFT_CLIENT_SECRET: Joi.string().optional(),

  // Email Configuration
  SMTP_HOST: Joi.string().hostname().optional(),
  SMTP_PORT: Joi.number().port().optional(),
  SMTP_SECURE: Joi.boolean().optional(),
  SMTP_USER: Joi.string().email().optional(),
  SMTP_PASS: Joi.string().optional(),
  FROM_EMAIL: Joi.string().email().optional(),
  FROM_NAME: Joi.string().default('SynapseAI'),

  // SMS Configuration (Twilio)
  TWILIO_ACCOUNT_SID: Joi.string().when('ENABLE_2FA', {
    is: true,
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  TWILIO_AUTH_TOKEN: Joi.string().when('ENABLE_2FA', {
    is: true,
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  TWILIO_PHONE_NUMBER: Joi.string().pattern(/^\+[1-9]\d{1,14}$/).when('ENABLE_2FA', {
    is: true,
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),

  // Payment Configuration (Stripe)
  STRIPE_SECRET_KEY: Joi.string().pattern(/^sk_(test_|live_)/).optional(),
  STRIPE_PUBLISHABLE_KEY: Joi.string().pattern(/^pk_(test_|live_)/).optional(),
  STRIPE_WEBHOOK_SECRET: Joi.string().pattern(/^whsec_/).optional(),

  // AI Providers
  OPENAI_API_KEY: Joi.string().optional(),
  OPENAI_ORGANIZATION: Joi.string().optional(),

  // External APIs
  GOOGLE_SEARCH_API_KEY: Joi.string().optional(),
  GOOGLE_SEARCH_ENGINE_ID: Joi.string().optional(),
  OPENWEATHER_API_KEY: Joi.string().optional(),

  // File Upload Configuration
  MAX_FILE_SIZE: Joi.number().positive().default(10485760), // 10MB
  UPLOAD_DEST: Joi.string().default('./uploads'),
  ALLOWED_FILE_TYPES: Joi.string().default('image/jpeg,image/png,image/gif,image/webp'),

  // Rate Limiting
  RATE_LIMIT_TTL: Joi.number().positive().default(60),
  RATE_LIMIT_LIMIT: Joi.number().positive().default(100),
  RATE_LIMIT_AUTH_TTL: Joi.number().positive().default(900),
  RATE_LIMIT_AUTH_LIMIT: Joi.number().positive().default(5),

  // Security
  BCRYPT_ROUNDS: Joi.number().min(10).max(15).default(12),
  SESSION_SECRET: Joi.string().min(32).optional(),
  COOKIE_SECRET: Joi.string().min(32).optional(),

  // Monitoring & Logging
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug', 'verbose')
    .default('info'),
  SENTRY_DSN: Joi.string().uri().optional(),

  // Feature Flags
  ENABLE_REGISTRATION: Joi.boolean().default(true),
  ENABLE_EMAIL_VERIFICATION: Joi.boolean().default(true),
  ENABLE_2FA: Joi.boolean().default(true),
  ENABLE_MAGIC_LINKS: Joi.boolean().default(true),
  ENABLE_OAUTH: Joi.boolean().default(true),

  // Development
  SWAGGER_ENABLED: Joi.boolean().default(true),
  DEBUG_MODE: Joi.boolean().default(false),
});

export interface ConfigVariables {
  // Server Configuration
  NODE_ENV?: 'development' | 'production' | 'test' | 'staging';
  PORT?: number;
  API_PREFIX?: string;
  CORS_ORIGIN?: string;

  // Database Configuration
  DATABASE_URL?: string;
  REDIS_URL?: string;

  // JWT Configuration
  JWT_SECRET?: string;
  JWT_REFRESH_SECRET?: string;
  JWT_EXPIRES_IN?: string;
  JWT_REFRESH_EXPIRES_IN?: string;

  // OAuth Providers
  GOOGLE_CLIENT_ID?: string;
  GOOGLE_CLIENT_SECRET?: string;
  GITHUB_CLIENT_ID?: string;
  GITHUB_CLIENT_SECRET?: string;
  MICROSOFT_CLIENT_ID?: string;
  MICROSOFT_CLIENT_SECRET?: string;

  // Email Configuration
  SMTP_HOST?: string;
  SMTP_PORT?: number;
  SMTP_SECURE?: boolean;
  SMTP_USER?: string;
  SMTP_PASS?: string;
  FROM_EMAIL?: string;
  FROM_NAME?: string;

  // SMS Configuration
  TWILIO_ACCOUNT_SID?: string;
  TWILIO_AUTH_TOKEN?: string;
  TWILIO_PHONE_NUMBER?: string;

  // Payment Configuration
  STRIPE_SECRET_KEY?: string;
  STRIPE_PUBLISHABLE_KEY?: string;
  STRIPE_WEBHOOK_SECRET?: string;

  // AI Providers
  OPENAI_API_KEY?: string;
  OPENAI_ORGANIZATION?: string;

  // External APIs
  GOOGLE_SEARCH_API_KEY?: string;
  GOOGLE_SEARCH_ENGINE_ID?: string;
  OPENWEATHER_API_KEY?: string;

  // File Upload Configuration
  MAX_FILE_SIZE?: number;
  UPLOAD_DEST?: string;
  ALLOWED_FILE_TYPES?: string;

  // Rate Limiting
  RATE_LIMIT_TTL?: number;
  RATE_LIMIT_LIMIT?: number;
  RATE_LIMIT_AUTH_TTL?: number;
  RATE_LIMIT_AUTH_LIMIT?: number;

  // Security
  BCRYPT_ROUNDS?: number;
  SESSION_SECRET?: string;
  COOKIE_SECRET?: string;

  // Monitoring & Logging
  LOG_LEVEL?: 'error' | 'warn' | 'info' | 'debug' | 'verbose';
  SENTRY_DSN?: string;

  // Feature Flags
  ENABLE_REGISTRATION?: boolean;
  ENABLE_EMAIL_VERIFICATION?: boolean;
  ENABLE_2FA?: boolean;
  ENABLE_MAGIC_LINKS?: boolean;
  ENABLE_OAUTH?: boolean;

  // Development
  SWAGGER_ENABLED?: boolean;
  DEBUG_MODE?: boolean;
}
