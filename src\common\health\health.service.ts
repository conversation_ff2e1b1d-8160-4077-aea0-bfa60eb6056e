import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '@/database/prisma.service';
import { RedisService } from '@/database/redis.service';

export interface ServiceHealth {
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime?: number;
  error?: string;
  details?: Record<string, unknown>;
}

export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: ServiceHealth;
    redis: ServiceHealth;
    openai?: ServiceHealth;
    email?: ServiceHealth;
  };
}

@Injectable()
export class HealthService {
  private readonly startTime: number;
  private readonly version: string;
  private readonly environment: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
    private readonly redisService: RedisService,
  ) {
    this.startTime = Date.now();
    this.version = '1.0.0'; // This would come from package.json in real app
    this.environment = this.configService?.get<string>('NODE_ENV') || process.env['NODE_ENV'] || 'development';
  }

  /**
   * Get comprehensive health status
   */
  async getHealthStatus(): Promise<HealthStatus> {
    const timestamp = new Date().toISOString();
    const uptime = Date.now() - this.startTime;

    const [databaseHealth, redisHealth] = await Promise.all([
      this.checkDatabaseHealth(),
      this.checkRedisHealth(),
    ]);

    // Determine overall status
    const services = { database: databaseHealth, redis: redisHealth };
    const overallStatus = this.determineOverallStatus(services);

    return {
      status: overallStatus,
      timestamp,
      uptime,
      version: this.version,
      environment: this.environment,
      services,
    };
  }

  /**
   * Check if application is ready to serve requests
   */
  async isReady(): Promise<boolean> {
    try {
      const [databaseReady, redisReady] = await Promise.all([
        this.isDatabaseReady(),
        this.isRedisReady(),
      ]);

      return databaseReady && redisReady;
    } catch {
      return false;
    }
  }

  /**
   * Check if application is alive (basic liveness check)
   */
  isAlive(): boolean {
    return true; // If this method is called, the app is alive
  }

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
      // Simple query to check database connectivity
      await this.prismaService['client'].$queryRaw`SELECT 1`;

      const responseTime = Date.now() - startTime;

      return {
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        responseTime,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown database error',
      };
    }
  }

  /**
   * Check Redis health
   */
  private async checkRedisHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
      // Simple ping to check Redis connectivity
      const result = await this.redisService.ping();

      if (result !== 'PONG') {
        throw new Error('Redis ping failed');
      }

      const responseTime = Date.now() - startTime;

      return {
        status: responseTime < 100 ? 'healthy' : 'degraded',
        responseTime,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown Redis error',
      };
    }
  }

  /**
   * Check if database is ready
   */
  private async isDatabaseReady(): Promise<boolean> {
    try {
      await this.prismaService['client'].$queryRaw`SELECT 1`;
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if Redis is ready
   */
  private async isRedisReady(): Promise<boolean> {
    try {
      const result = await this.redisService.ping();
      return result === 'PONG';
    } catch {
      return false;
    }
  }

  /**
   * Determine overall system status based on service health
   */
  private determineOverallStatus(
    services: Record<string, ServiceHealth>,
  ): 'healthy' | 'unhealthy' | 'degraded' {
    const statuses = Object.values(services).map(service => service.status);

    if (statuses.every(status => status === 'healthy')) {
      return 'healthy';
    }

    if (statuses.some(status => status === 'unhealthy')) {
      return 'unhealthy';
    }

    return 'degraded';
  }

  /**
   * Get system metrics
   */
  getSystemMetrics(): {
    uptime: number;
    memory: NodeJS.MemoryUsage;
    cpu: NodeJS.CpuUsage;
    version: string;
    environment: string;
  } {
    return {
      uptime: Date.now() - this.startTime,
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      version: this.version,
      environment: this.environment,
    };
  }
}
