import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import {
  BaseAIProvider,
  AICompletionRequest,
  AICompletionResponse,
  AIStreamChunk,
  AIProviderConfig,
  AIProviderUsage,
} from '../interfaces/ai-provider.interface';

@Injectable()
export class OpenRouterProvider extends BaseAIProvider {
  readonly name = 'openrouter';
  private readonly logger = new Logger(OpenRouterProvider.name);
  private client: AxiosInstance | null = null;
  readonly config: AIProviderConfig;

  constructor(private readonly configService: ConfigService) {
    super();

    const apiKey = this.configService?.get<string>('OPENROUTER_API_KEY') || process.env['OPENROUTER_API_KEY'];
    const appName = this.configService?.get<string>('APP_NAME') || process.env['APP_NAME'] || 'SynapseAI';
    const appUrl = this.configService?.get<string>('APP_URL') || process.env['APP_URL'] || 'https://synapseai.com';

    this.config = {
      name: this.name,
      apiKey,
      baseUrl: 'https://openrouter.ai/api/v1',
      models: [
        'openai/gpt-4-turbo-preview',
        'openai/gpt-4',
        'openai/gpt-3.5-turbo',
        'anthropic/claude-3-opus',
        'anthropic/claude-3-sonnet',
        'anthropic/claude-3-haiku',
        'google/gemini-pro',
        'meta-llama/llama-3-70b-instruct',
        'meta-llama/llama-3-8b-instruct',
        'mistralai/mixtral-8x7b-instruct',
        'cohere/command-r-plus',
      ],
      maxTokens: 128000,
      supportsStreaming: true,
      supportsFunctions: true,
      isAvailable: !!apiKey,
    };

    if (this.isAvailable()) {
      this.client = axios.create({
        baseURL: this.config.baseUrl,
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': appUrl,
          'X-Title': appName,
        },
        timeout: 120000, // OpenRouter can be slower
      });
      this.logger.log('✅ OpenRouter provider initialized successfully');
    } else {
      this.logger.warn('⚠️ OpenRouter provider not available - missing API key');
    }
  }

  isAvailable(): boolean {
    return this.config.isAvailable;
  }

  getModels(): string[] {
    return this.config.models;
  }

  validateConfig(): boolean {
    return !!this.config.apiKey;
  }

  async createCompletion(request: AICompletionRequest): Promise<AICompletionResponse> {
    if (!this.client) {
      throw new Error('OpenRouter client not initialized - missing API key');
    }

    try {
      const response = await this.client.post('/chat/completions', {
        model: request.model || 'openai/gpt-3.5-turbo',
        messages: this.formatMessages(request.messages),
        temperature: request.temperature,
        max_tokens: request.max_tokens,
        top_p: request.top_p,
        frequency_penalty: request.frequency_penalty,
        presence_penalty: request.presence_penalty,
        stop: request.stop,
        stream: false,
        ...(request.functions && { functions: request.functions }),
        ...(request.function_call && { function_call: request.function_call }),
      });

      return {
        id: response.data.id,
        object: response.data.object,
        created: response.data.created,
        model: response.data.model,
        choices: response.data.choices.map((choice: any) => ({
          index: choice.index,
          message: {
            role: choice.message.role,
            content: choice.message.content || '',
            ...(choice.message.function_call && {
              function_call: choice.message.function_call,
            }),
          },
          finish_reason: choice.finish_reason || '',
        })),
        usage: {
          prompt_tokens: response.data.usage?.prompt_tokens || 0,
          completion_tokens: response.data.usage?.completion_tokens || 0,
          total_tokens: response.data.usage?.total_tokens || 0,
        },
      };
    } catch (error) {
      this.handleError(error, 'completion failed');
    }
  }

  async *createStreamCompletion(request: AICompletionRequest): AsyncIterable<AIStreamChunk> {
    if (!this.client) {
      throw new Error('OpenRouter client not initialized - missing API key');
    }

    try {
      const response = await this.client.post('/chat/completions', {
        model: request.model || 'openai/gpt-3.5-turbo',
        messages: this.formatMessages(request.messages),
        temperature: request.temperature,
        max_tokens: request.max_tokens,
        top_p: request.top_p,
        frequency_penalty: request.frequency_penalty,
        presence_penalty: request.presence_penalty,
        stop: request.stop,
        stream: true,
        ...(request.functions && { functions: request.functions }),
        ...(request.function_call && { function_call: request.function_call }),
      }, {
        responseType: 'stream',
      });

      let buffer = '';

      for await (const chunk of response.data) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              return;
            }

            try {
              const parsed = JSON.parse(data);
              yield {
                id: parsed.id,
                object: parsed.object,
                created: parsed.created,
                model: parsed.model,
                choices: parsed.choices.map((choice: any) => ({
                  index: choice.index,
                  delta: {
                    role: choice.delta?.role,
                    content: choice.delta?.content,
                    ...(choice.delta?.function_call && {
                      function_call: choice.delta.function_call,
                    }),
                  },
                  finish_reason: choice.finish_reason,
                })),
              };
            } catch (parseError) {
              // Skip invalid JSON chunks
              continue;
            }
          }
        }
      }
    } catch (error) {
      this.handleError(error, 'stream completion failed');
    }
  }

  calculateCost(usage: AIProviderUsage, model: string): number {
    // OpenRouter pricing varies by model - these are approximate rates
    const pricing: Record<string, { input: number; output: number }> = {
      'openai/gpt-4-turbo-preview': { input: 0.01, output: 0.03 },
      'openai/gpt-4': { input: 0.03, output: 0.06 },
      'openai/gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
      'anthropic/claude-3-opus': { input: 0.015, output: 0.075 },
      'anthropic/claude-3-sonnet': { input: 0.003, output: 0.015 },
      'anthropic/claude-3-haiku': { input: 0.00025, output: 0.00125 },
      'google/gemini-pro': { input: 0.0005, output: 0.0015 },
      'meta-llama/llama-3-70b-instruct': { input: 0.0009, output: 0.0009 },
      'meta-llama/llama-3-8b-instruct': { input: 0.0002, output: 0.0002 },
      'mistralai/mixtral-8x7b-instruct': { input: 0.0006, output: 0.0006 },
      'cohere/command-r-plus': { input: 0.003, output: 0.015 },
    };

    const modelPricing = pricing[model] || pricing['openai/gpt-3.5-turbo'];
    const inputCost = (usage.promptTokens / 1000) * modelPricing.input;
    const outputCost = (usage.completionTokens / 1000) * modelPricing.output;

    return inputCost + outputCost;
  }

  async testConnection(): Promise<boolean> {
    if (!this.client) {
      return false;
    }

    try {
      await this.client.get('/models');
      return true;
    } catch (error) {
      this.logger.error('OpenRouter connection test failed:', error);
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    if (!this.client) {
      return this.config.models;
    }

    try {
      const response = await this.client.get('/models');
      return response.data.data
        .map((model: any) => model.id)
        .filter((id: string) => this.config.models.includes(id))
        .sort();
    } catch (error) {
      this.logger.warn('Failed to fetch OpenRouter models, using defaults:', error);
      return this.config.models;
    }
  }
}
