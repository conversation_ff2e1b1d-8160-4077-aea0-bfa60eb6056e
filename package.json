{"name": "synapseai", "version": "1.0.0", "description": "Visual AI Agent Builder Platform", "main": "dist/main.js", "scripts": {"build": "tsc && tsc-alias", "start": "node dist/main.js", "dev": "tsx watch --clear-screen=false src/main.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "typecheck": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "docker:build": "docker build -t synapseai-backend .", "docker:run": "docker run -p 3001:3001 synapseai-backend"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@nestjs/common": "^10.3.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.0", "@nestjs/platform-socket.io": "^10.3.0", "@nestjs/swagger": "^7.2.0", "@nestjs/throttler": "^5.1.1", "@nestjs/websockets": "^10.3.0", "@prisma/client": "^6.10.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^17.0.0", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.12.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.8", "openai": "^4.104.0", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "prisma": "^5.8.1", "qrcode": "^1.5.3", "react-hook-form": "^7.59.0", "redis": "^4.6.12", "reflect-metadata": "^0.2.1", "rxjs": "^7.8.1", "sharp": "^0.33.2", "socket.io": "^4.7.4", "speakeasy": "^2.0.0", "stripe": "^14.14.0", "tailwind-merge": "^3.3.1", "twilio": "^4.20.1", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.11.5", "@types/nodemailer": "^6.4.14", "@types/passport-github2": "^1.2.9", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-jwt": "^4.0.0", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.4", "supertest": "^6.3.4", "ts-jest": "^29.1.1", "tsc-alias": "^1.8.8", "tsx": "^4.7.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["ai", "agents", "chatbots", "visual-builder", "no-code", "<PERSON><PERSON><PERSON>", "typescript"], "author": "SynapseAI Team", "license": "MIT"}