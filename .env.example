# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/synapseai"
REDIS_URL="redis://localhost:6379"

# JWT Configuration
JWT_SECRET="your-super-secure-jwt-secret-key-here"
JWT_REFRESH_SECRET="your-super-secure-refresh-secret-key-here"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server Configuration
PORT=3001
NODE_ENV="development"
API_PREFIX="api/v1"
CORS_ORIGIN="http://localhost:3000"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
MICROSOFT_CLIENT_ID="your-microsoft-client-id"
MICROSOFT_CLIENT_SECRET="your-microsoft-client-secret"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"
FROM_NAME="SynapseAI"

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# Payment Configuration (Stripe)
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"
STRIPE_WEBHOOK_SECRET="whsec_your-webhook-secret"

# AI Providers
OPENAI_API_KEY=
OPENAI_ORGANIZATION="your-openai-organization-id"

# External APIs
GOOGLE_SEARCH_API_KEY="your-google-search-api-key"
GOOGLE_SEARCH_ENGINE_ID="your-search-engine-id"
OPENWEATHER_API_KEY="your-openweather-api-key"

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_DEST="./uploads"
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,image/webp"

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100
RATE_LIMIT_AUTH_TTL=900
RATE_LIMIT_AUTH_LIMIT=5

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-key"
COOKIE_SECRET="your-cookie-secret-key"

# Monitoring & Logging
LOG_LEVEL="info"
SENTRY_DSN="your-sentry-dsn"

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_2FA=true
ENABLE_MAGIC_LINKS=true
ENABLE_OAUTH=true

# Development
SWAGGER_ENABLED=true
DEBUG_MODE=false
