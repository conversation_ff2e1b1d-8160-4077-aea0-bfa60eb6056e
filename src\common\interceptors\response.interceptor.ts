import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request, Response } from 'express';

export interface SuccessResponse<T = unknown> {
  success: true;
  data: T;
  meta?: {
    timestamp: string;
    requestId?: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, SuccessResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<SuccessResponse<T>> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    
    return next.handle().pipe(
      map((data: T) => {
        const requestId = request.headers['x-request-id'] as string;
        const timestamp = new Date().toISOString();

        // Handle different response types
        if (this.isFileResponse(data)) {
          // Don't transform file responses
          return data as unknown as SuccessResponse<T>;
        }

        if (this.isRedirectResponse(response)) {
          // Don't transform redirect responses
          return data as unknown as SuccessResponse<T>;
        }

        // Handle paginated responses
        if (this.isPaginatedResponse(data)) {
          const paginatedData = data as unknown as {
            data: unknown[];
            meta: PaginationMeta;
          };

          return {
            success: true,
            data: paginatedData.data as T,
            meta: {
              timestamp,
              requestId,
              pagination: paginatedData.meta,
            },
          };
        }

        // Handle standard responses
        return {
          success: true,
          data,
          meta: {
            timestamp,
            requestId,
          },
        };
      }),
    );
  }

  private isFileResponse(data: unknown): boolean {
    return (
      data instanceof Buffer ||
      (typeof data === 'object' &&
        data !== null &&
        'pipe' in data &&
        typeof (data as any).pipe === 'function')
    );
  }

  private isRedirectResponse(response: Response): boolean {
    return response.statusCode >= 300 && response.statusCode < 400;
  }

  private isPaginatedResponse(data: unknown): boolean {
    return (
      typeof data === 'object' &&
      data !== null &&
      'data' in data &&
      'meta' in data &&
      Array.isArray((data as any).data) &&
      typeof (data as any).meta === 'object' &&
      'page' in (data as any).meta &&
      'limit' in (data as any).meta &&
      'total' in (data as any).meta
    );
  }
}

// Utility function to create paginated responses
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
): { data: T[]; meta: PaginationMeta } {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;

  return {
    data,
    meta: {
      page,
      limit,
      total,
      totalPages,
      hasNext,
      hasPrev,
    },
  };
}

// Utility function to extract pagination parameters from query
export function extractPaginationParams(query: Record<string, unknown>): {
  page: number;
  limit: number;
  skip: number;
} {
  const page = Math.max(1, parseInt(String(query['page'] || 1), 10));
  const limit = Math.min(100, Math.max(1, parseInt(String(query['limit'] || 10), 10)));
  const skip = (page - 1) * limit;

  return { page, limit, skip };
}
