import { Injectable } from '@nestjs/common';
import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { ValidationException } from '@/common/filters/global-exception.filter';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  details?: ValidationError[];
}

@Injectable()
export class ValidationService {
  /**
   * Validate an object against a DTO class
   */
  async validateDto<T extends object>(
    dtoClass: new () => T,
    data: unknown,
  ): Promise<ValidationResult> {
    const dto = plainToClass(dtoClass, data);
    const errors = await validate(dto);

    if (errors.length > 0) {
      return {
        isValid: false,
        errors: this.formatValidationErrors(errors),
        details: errors,
      };
    }

    return {
      isValid: true,
      errors: [],
    };
  }

  /**
   * Validate and throw exception if invalid
   */
  async validateDtoOrThrow<T extends object>(
    dtoClass: new () => T,
    data: unknown,
  ): Promise<T> {
    const dto = plainToClass(dtoClass, data);
    const errors = await validate(dto);

    if (errors.length > 0) {
      const errorMessages = this.formatValidationErrors(errors);
      throw new ValidationException(
        'Validation failed',
        {
          errors: errorMessages,
          details: errors,
        },
      );
    }

    return dto;
  }

  /**
   * Validate email format
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): ValidationResult {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (password.length > 128) {
      errors.push('Password must not exceed 128 characters');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Check for common patterns
    if (/(.)\1{2,}/.test(password)) {
      errors.push('Password must not contain repeated characters');
    }

    if (/123|abc|qwe|password|admin/i.test(password)) {
      errors.push('Password must not contain common patterns');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate phone number format
   */
  isValidPhoneNumber(phoneNumber: string): boolean {
    // International format: +1234567890 to +123456789012345
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * Validate URL format
   */
  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate UUID format
   */
  isValidUuid(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Validate JSON string
   */
  isValidJson(jsonString: string): boolean {
    try {
      JSON.parse(jsonString);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate file type
   */
  isValidFileType(filename: string, allowedTypes: string[]): boolean {
    const extension = filename.toLowerCase().split('.').pop();
    return extension ? allowedTypes.includes(extension) : false;
  }

  /**
   * Validate file size
   */
  isValidFileSize(fileSize: number, maxSize: number): boolean {
    return fileSize <= maxSize;
  }

  /**
   * Sanitize string input
   */
  sanitizeString(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/['"]/g, '') // Remove quotes
      .substring(0, 1000); // Limit length
  }

  /**
   * Validate and sanitize HTML content
   */
  sanitizeHtml(html: string): string {
    // Basic HTML sanitization - in production, use a library like DOMPurify
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(page?: number, limit?: number): {
    page: number;
    limit: number;
    skip: number;
  } {
    const validPage = Math.max(1, page || 1);
    const validLimit = Math.min(100, Math.max(1, limit || 10));
    const skip = (validPage - 1) * validLimit;

    return { page: validPage, limit: validLimit, skip };
  }

  /**
   * Format validation errors into readable messages
   */
  private formatValidationErrors(errors: ValidationError[]): string[] {
    const messages: string[] = [];

    for (const error of errors) {
      if (error.constraints) {
        messages.push(...Object.values(error.constraints));
      }

      if (error.children && error.children.length > 0) {
        messages.push(...this.formatValidationErrors(error.children));
      }
    }

    return messages;
  }

  /**
   * Validate agent flow configuration
   */
  validateAgentFlow(flow: unknown): ValidationResult {
    const errors: string[] = [];

    if (!flow || typeof flow !== 'object') {
      errors.push('Flow must be a valid object');
      return { isValid: false, errors };
    }

    const flowObj = flow as Record<string, unknown>;

    if (!Array.isArray(flowObj.nodes)) {
      errors.push('Flow must contain a nodes array');
    }

    if (!Array.isArray(flowObj.connections)) {
      errors.push('Flow must contain a connections array');
    }

    // Validate nodes
    if (Array.isArray(flowObj.nodes)) {
      for (const [index, node] of flowObj.nodes.entries()) {
        if (!node || typeof node !== 'object') {
          errors.push(`Node at index ${index} must be a valid object`);
          continue;
        }

        const nodeObj = node as Record<string, unknown>;

        if (!nodeObj.id || typeof nodeObj.id !== 'string') {
          errors.push(`Node at index ${index} must have a valid id`);
        }

        if (!nodeObj.type || typeof nodeObj.type !== 'string') {
          errors.push(`Node at index ${index} must have a valid type`);
        }

        const validTypes = ['message', 'tool', 'condition', 'human', 'end'];
        if (nodeObj.type && !validTypes.includes(nodeObj.type as string)) {
          errors.push(`Node at index ${index} has invalid type: ${nodeObj.type}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
