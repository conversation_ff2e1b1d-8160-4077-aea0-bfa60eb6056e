module.exports = {
  displayName: 'SynapseAI Backend',
  preset: 'ts-jest',
  testEnvironment: 'node',
  rootDir: '.',
  testMatch: [
    '<rootDir>/src/**/*.spec.ts',
    '<rootDir>/test/**/*.spec.ts',
    '<rootDir>/test/**/*.test.ts',
  ],
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.spec.ts',
    '!src/**/*.test.ts',
    '!src/main.ts',
    '!src/**/*.interface.ts',
    '!src/**/*.dto.ts',
    '!src/**/*.entity.ts',
    '!src/**/*.module.ts',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/auth/(.*)$': '<rootDir>/src/auth/$1',
    '^@/users/(.*)$': '<rootDir>/src/users/$1',
    '^@/agents/(.*)$': '<rootDir>/src/agents/$1',
    '^@/tools/(.*)$': '<rootDir>/src/tools/$1',
    '^@/providers/(.*)$': '<rootDir>/src/providers/$1',
    '^@/landing/(.*)$': '<rootDir>/src/landing/$1',
    '^@/payments/(.*)$': '<rootDir>/src/payments/$1',
    '^@/analytics/(.*)$': '<rootDir>/src/analytics/$1',
    '^@/email/(.*)$': '<rootDir>/src/email/$1',
    '^@/common/(.*)$': '<rootDir>/src/common/$1',
    '^@/config/(.*)$': '<rootDir>/src/config/$1',
    '^@/database/(.*)$': '<rootDir>/src/database/$1',
    '^@/websocket/(.*)$': '<rootDir>/src/websocket/$1',
  },
  setupFilesAfterEnv: ['<rootDir>/test/setup.ts'],
  testTimeout: 30000,
  verbose: true,
  forceExit: true,
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
  errorOnDeprecated: true,
  detectOpenHandles: true,
  detectLeaks: true,
};
